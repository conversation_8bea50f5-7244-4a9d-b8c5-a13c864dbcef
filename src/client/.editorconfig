root = true
# Editor configuration, see https://editorconfig.org

[*.cs]
charset = utf-8
indent_size = 4
indent_style = space
end_of_line = lf
max_line_length = 300
insert_final_newline = false
trim_trailing_whitespace = true

# .NET Style Rules
# https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/language-rules
csharp_prefer_braces = true
csharp_preferred_modifier_order = public, private, protected, internal, new, abstract, virtual, sealed, override, static, readonly, extern, unsafe, volatile, async:suggestion
csharp_style_pattern_matching_over_as_with_null_check = true
csharp_style_var_elsewhere = true:suggestion
csharp_style_var_for_built_in_types = true:suggestion
csharp_style_var_when_type_is_apparent = true:suggestion
dotnet_style_parentheses_in_arithmetic_binary_operators = never_if_unnecessary:none
dotnet_style_parentheses_in_other_binary_operators = never_if_unnecessary:none
dotnet_style_parentheses_in_relational_binary_operators = never_if_unnecessary:none
dotnet_style_predefined_type_for_locals_parameters_members = true:suggestion
dotnet_style_predefined_type_for_member_access = true:suggestion
dotnet_style_qualification_for_event = false:suggestion
dotnet_style_qualification_for_field = false:suggestion
dotnet_style_qualification_for_method = false:suggestion
dotnet_style_qualification_for_property = false:suggestion
dotnet_style_require_accessibility_modifiers = for_non_interface_members:suggestion

# .NET Formatting Rules
# https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/formatting-rules
csharp_indent_block_contents = true
csharp_indent_braces = false
csharp_indent_case_contents = true
csharp_indent_case_contents_when_block = true
csharp_indent_labels = flush_left
csharp_indent_switch_labels = true
csharp_new_line_before_catch = true
csharp_new_line_before_else = true
csharp_new_line_before_finally = true
csharp_new_line_before_members_in_object_initializers = false
csharp_new_line_before_open_brace = all
csharp_new_line_between_query_expression_clauses = true
csharp_preserve_single_line_blocks = true
csharp_space_after_cast = false
csharp_space_after_colon_in_inheritance_clause = true
csharp_space_after_comma = true
csharp_space_after_dot = false
csharp_space_after_keywords_in_control_flow_statements = true
csharp_space_after_semicolon_in_for_statement = true
csharp_space_around_binary_operators = before_and_after
csharp_space_before_colon_in_inheritance_clause = true
csharp_space_before_comma = false
csharp_space_before_dot = false
csharp_space_before_open_square_brackets = false
csharp_space_before_semicolon_in_for_statement = false
csharp_space_between_empty_square_brackets = false
csharp_space_between_method_call_empty_parameter_list_parentheses = false
csharp_space_between_method_call_name_and_opening_parenthesis = false
csharp_space_between_method_call_parameter_list_parentheses = false
csharp_space_between_method_declaration_empty_parameter_list_parentheses = false
csharp_space_between_method_declaration_name_and_open_parenthesis = false
csharp_space_between_method_declaration_parameter_list_parentheses = false
csharp_space_between_parentheses = false
csharp_space_between_square_brackets = false
csharp_style_namespace_declarations = block
csharp_using_directive_placement = outside_namespace:silent
dotnet_separate_import_directive_groups = false
dotnet_sort_system_directives_first = true
dotnet_style_namespace_match_folder = false

#.NET Naming Rules
# https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/naming-rules
dotnet_naming_rule.constants_rule.severity = warning
dotnet_naming_rule.constants_rule.style = upper_camel_case_style
dotnet_naming_rule.constants_rule.symbols = constants_symbols
dotnet_naming_rule.constants_rule.resharper_style = AA_BB
dotnet_naming_rule.private_constants_rule.severity = warning
dotnet_naming_rule.private_constants_rule.style = upper_camel_case_style
dotnet_naming_rule.private_constants_rule.symbols = private_constants_symbols
dotnet_naming_rule.private_instance_fields_rule.severity = warning
dotnet_naming_rule.private_instance_fields_rule.style = lower_camel_case_style
dotnet_naming_rule.private_instance_fields_rule.symbols = private_instance_fields_symbols
dotnet_naming_rule.private_static_fields_rule.severity = warning
dotnet_naming_rule.private_static_fields_rule.style = lower_camel_case_style
dotnet_naming_rule.private_static_fields_rule.symbols = private_static_fields_symbols
dotnet_naming_rule.private_static_readonly_rule.severity = warning
dotnet_naming_rule.private_static_readonly_rule.style = upper_camel_case_style
dotnet_naming_rule.private_static_readonly_rule.symbols = private_static_readonly_symbols
dotnet_naming_rule.public_fields_rule.severity = warning
dotnet_naming_rule.public_fields_rule.style = upper_camel_case_style
dotnet_naming_rule.public_fields_rule.symbols = public_fields_symbols
dotnet_naming_rule.static_readonly_rule.resharper_style = AA_BB
dotnet_naming_rule.static_readonly_rule.severity = warning
dotnet_naming_rule.static_readonly_rule.style = upper_camel_case_style
dotnet_naming_rule.static_readonly_rule.symbols = static_readonly_symbols
dotnet_naming_style.lower_camel_case_style.capitalization = camel_case
dotnet_naming_style.upper_camel_case_style.capitalization = pascal_case
dotnet_naming_symbols.constants_symbols.applicable_accessibilities = public, internal, protected, protected_internal, private_protected
dotnet_naming_symbols.constants_symbols.applicable_kinds = field
dotnet_naming_symbols.constants_symbols.required_modifiers = const
dotnet_naming_symbols.private_constants_symbols.applicable_accessibilities = private
dotnet_naming_symbols.private_constants_symbols.applicable_kinds = field
dotnet_naming_symbols.private_constants_symbols.required_modifiers = const
dotnet_naming_symbols.private_instance_fields_symbols.applicable_accessibilities = private
dotnet_naming_symbols.private_instance_fields_symbols.applicable_kinds = field
dotnet_naming_symbols.private_static_fields_symbols.applicable_accessibilities = private
dotnet_naming_symbols.private_static_fields_symbols.applicable_kinds = field
dotnet_naming_symbols.private_static_fields_symbols.required_modifiers = static
dotnet_naming_symbols.private_static_readonly_symbols.applicable_accessibilities = private
dotnet_naming_symbols.private_static_readonly_symbols.applicable_kinds = field
dotnet_naming_symbols.private_static_readonly_symbols.required_modifiers = static, readonly
dotnet_naming_symbols.public_fields_symbols.applicable_accessibilities = public, internal, protected, protected_internal, private_protected
dotnet_naming_symbols.public_fields_symbols.applicable_kinds = field
dotnet_naming_symbols.static_readonly_symbols.applicable_accessibilities = public, internal, protected, protected_internal, private_protected
dotnet_naming_symbols.static_readonly_symbols.applicable_kinds = field
dotnet_naming_symbols.static_readonly_symbols.required_modifiers = static, readonly

# ReSharper Style Rules
# https://www.jetbrains.com/help/resharper/EditorConfig_Index.html
resharper_accessor_owner_body = expression_body
resharper_align_first_arg_by_paren = false
resharper_align_linq_query = true
resharper_align_multiline_argument = false
resharper_align_multiline_array_and_object_initializer = false
resharper_align_multiline_array_initializer = true
resharper_align_multiline_binary_expressions_chain = false
resharper_align_multiline_calls_chain = false
resharper_align_multiline_ctor_init = true
resharper_align_multiline_expression = false
resharper_align_multiline_extends_list = false
resharper_align_multiline_for_stmt = false
resharper_align_multiline_implements_list = true
resharper_align_multiline_parameter = true
resharper_align_multiline_type_argument = true
resharper_align_multiline_type_parameter = true
resharper_align_multiple_declaration = false
resharper_align_multline_type_parameter_constrains = false
resharper_align_multline_type_parameter_list = false
resharper_align_ternary = align_not_nested
resharper_align_tuple_components = false
resharper_alignment_tab_fill_style = use_spaces
resharper_allow_alias = true
resharper_arguments_anonymous_function = positional
resharper_arguments_literal = positional
resharper_arguments_named = positional
resharper_arguments_other = positional
resharper_arguments_skip_single = false
resharper_arguments_string_literal = positional
resharper_attribute_indent = align_by_first_attribute
resharper_blank_lines_after_block_statements = 1
resharper_blank_lines_after_control_transfer_statements = 1
resharper_blank_lines_after_imports = 1
resharper_blank_lines_after_multiline_statements = 1
resharper_blank_lines_after_options = 1
resharper_blank_lines_after_start_comment = 0
resharper_blank_lines_after_using_list = 1
resharper_blank_lines_around_auto_property = 0
resharper_blank_lines_around_class_definition = 1
resharper_blank_lines_around_field = 0
resharper_blank_lines_around_function_declaration = 0
resharper_blank_lines_around_function_definition = 1
resharper_blank_lines_around_global_attribute = 0
resharper_blank_lines_around_invocable = 0
resharper_blank_lines_around_local_method = 1
resharper_blank_lines_around_namespace = 0
resharper_blank_lines_around_other_declaration = 0
resharper_blank_lines_around_property = 1
resharper_blank_lines_around_region = 1
resharper_blank_lines_around_single_line_auto_property = 0
resharper_blank_lines_around_single_line_field = 0
resharper_blank_lines_around_single_line_function_definition = 0
resharper_blank_lines_around_single_line_invocable = 0
resharper_blank_lines_around_single_line_local_method = 1
resharper_blank_lines_around_single_line_property = 0
resharper_blank_lines_around_type = 1
resharper_blank_lines_before_block_statements = 1
resharper_blank_lines_before_control_transfer_statements = 1
resharper_blank_lines_before_multiline_statements = 1
resharper_blank_lines_before_single_line_comment = 1
resharper_blank_lines_inside_namespace = 0
resharper_blank_lines_inside_region = 1
resharper_blank_lines_inside_type = 0
resharper_brace_style = next_line
resharper_braces_for_dowhile = required
resharper_braces_for_fixed = required
resharper_braces_for_for = required
resharper_braces_for_foreach = required
resharper_braces_for_ifelse = required
resharper_braces_for_lock = required
resharper_braces_for_using = required
resharper_braces_for_while = required
resharper_braces_redundant = true
resharper_break_template_declaration = line_break
resharper_can_use_global_alias = true
resharper_case_block_braces = next_line
resharper_constructor_or_destructor_body = block_body
resharper_continuous_indent_multiplier = 1
resharper_continuous_line_indent = single
resharper_csharp_align_multiline_binary_expressions_chain = true
resharper_csharp_align_multiline_parameter = true
resharper_csharp_indent_nested_fixed_stmt = false
resharper_csharp_indent_nested_for_stmt = false
resharper_csharp_indent_nested_foreach_stmt = false
resharper_csharp_indent_nested_lock_stmt = false
resharper_csharp_indent_nested_usings_stmt = false
resharper_csharp_indent_nested_while_stmt = false
resharper_default_value_when_type_evident = default_literal
resharper_default_value_when_type_not_evident = default_literal
resharper_disable_space_changes_before_trailing_comment = false
resharper_empty_block_style = together_same_line
resharper_expression_braces = inside
resharper_extra_spaces = remove_all
resharper_force_chop_compound_do_expression = false
resharper_force_chop_compound_if_expression = false
resharper_force_chop_compound_while_expression = false
resharper_function_declaration_return_type_style = do_not_change
resharper_function_definition_return_type_style = do_not_change
resharper_indent_access_specifiers_from_class = false
resharper_indent_anonymous_method_block = false
resharper_indent_case_from_select = true
resharper_indent_child_elements = RemoveIndent
resharper_indent_comment = true
resharper_indent_inside_namespace = true
resharper_indent_invocation_pars = inside
resharper_indent_method_decl_pars = inside
resharper_indent_nested_fixed_stmt = false
resharper_indent_nested_for_stmt = false
resharper_indent_nested_foreach_stmt = false
resharper_indent_nested_lock_stmt = false
resharper_indent_nested_usings_stmt = false
resharper_indent_nested_while_stmt = false
resharper_indent_pars = inside
resharper_indent_preprocessor_directives = none
resharper_indent_preprocessor_if = no_indent
resharper_indent_preprocessor_other = no_indent
resharper_indent_preprocessor_region = usual_indent
resharper_indent_statement_pars = inside
resharper_indent_text = RemoveIndent
resharper_indent_type_constraints = true
resharper_indent_typearg_angles = inside
resharper_indent_typeparam_angles = inside
resharper_indent_wrapped_function_names = false
resharper_int_align = false
resharper_int_align_comments = false
resharper_int_align_declaration_names = false
resharper_int_align_eq = false
resharper_keep_blank_lines_in_code = 1
resharper_keep_blank_lines_in_declarations = 1
resharper_keep_existing_attribute_arrangement = true
resharper_keep_existing_declaration_block_arrangement = true
resharper_keep_existing_declaration_parens_arrangement = true
resharper_keep_existing_embedded_arrangement = true
resharper_keep_existing_embedded_block_arrangement = true
resharper_keep_existing_enum_arrangement = false
resharper_keep_existing_expr_member_arrangement = true
resharper_keep_existing_invocation_parens_arrangement = true
resharper_keep_user_linebreaks = true
resharper_line_break_after_colon_in_member_initializer_lists = do_not_change
resharper_line_break_after_comma_in_member_initializer_lists = false
resharper_line_break_before_comma_in_member_initializer_lists = false
resharper_linebreak_before_multiline_elements = true
resharper_linebreak_before_singleline_elements = false
resharper_linebreaks_inside_tags_for_elements_longer_than = 2147483647
resharper_linebreaks_inside_tags_for_elements_with_child_elements = true
resharper_linebreaks_inside_tags_for_multiline_elements = true
resharper_linkage_specification_braces = end_of_line
resharper_linkage_specification_indentation = none
resharper_local_function_body = block_body
resharper_max_array_initializer_elements_on_line = 10000
resharper_max_attribute_length_for_same_line = 70
resharper_max_blank_lines_between_tags = 1
resharper_max_enum_members_on_line = 3
resharper_max_formal_parameters_on_line = 10000
resharper_max_initializer_elements_on_line = 4
resharper_max_invocation_arguments_on_line = 10000
resharper_member_initializer_list_style = do_not_change
resharper_method_or_operator_body = block_body
resharper_namespace_declaration_braces = next_line
resharper_namespace_indentation = all
resharper_nested_ternary_style = autodetect
resharper_new_line_before_catch = true
resharper_new_line_before_else = true
resharper_new_line_before_while = false
resharper_object_creation_when_type_evident = target_typed
resharper_old_engine = false
resharper_outdent_binary_ops = true
resharper_outdent_commas = false
resharper_outdent_dots = false
resharper_parentheses_redundancy_style = remove_if_not_clarifies_precedence
resharper_pi_attribute_style = do_not_touch
resharper_place_accessor_attribute_on_same_line = if_owner_is_single_line
resharper_place_accessorholder_attribute_on_same_line = false
resharper_place_constructor_initializer_on_same_line = true
resharper_place_event_attribute_on_same_line = false
resharper_place_expr_accessor_on_single_line = if_owner_is_single_line
resharper_place_expr_method_on_single_line = if_owner_is_single_line
resharper_place_expr_property_on_single_line = if_owner_is_single_line
resharper_place_field_attribute_on_same_line = true
resharper_place_linq_into_on_new_line = false
resharper_place_method_attribute_on_same_line = true
resharper_place_namespace_definitions_on_same_line = false
resharper_place_property_attribute_on_same_line = false
resharper_place_simple_case_statement_on_same_line = false
resharper_place_simple_embedded_statement_on_same_line = if_owner_is_single_line
resharper_place_simple_initializer_on_single_line = true
resharper_place_type_attribute_on_same_line = false
resharper_place_type_constraints_on_same_line = true
resharper_prefer_explicit_discard_declaration = false
resharper_prefer_qualified_reference = false
resharper_prefer_separate_deconstructed_variables_declaration = false
resharper_qualified_using_at_nested_scope = false
resharper_remove_blank_lines_near_braces_in_code = true
resharper_remove_blank_lines_near_braces_in_declarations = false
resharper_simple_block_style = do_not_change
resharper_simple_case_statement_style = do_not_change
resharper_simple_embedded_statement_style = do_not_change
resharper_space_after_attribute_target_colon = true
resharper_space_after_attributes = true
resharper_space_after_cast = false
resharper_space_after_colon = true
resharper_space_after_colon_in_case = true
resharper_space_after_comma = true
resharper_space_after_for_colon = true
resharper_space_after_keywords_in_control_flow_statements = true
resharper_space_after_last_attribute = false
resharper_space_after_last_pi_attribute = false
resharper_space_after_operator_keyword = true
resharper_space_after_ptr_in_data_member = true
resharper_space_after_ptr_in_data_members = false
resharper_space_after_ptr_in_method = true
resharper_space_after_semicolon_in_for_statement = true
resharper_space_after_ternary_colon = true
resharper_space_after_ternary_quest = true
resharper_space_after_type_parameter_constraint_colon = true
resharper_space_after_unary_operator = false
resharper_space_around_additive_op = true
resharper_space_around_alias_eq = true
resharper_space_around_assignment_op = true
resharper_space_around_assignment_operator = true
resharper_space_around_deref_in_trailing_return_type = true
resharper_space_around_lambda_arrow = true
resharper_space_around_member_access_operator = false
resharper_space_around_relational_op = true
resharper_space_around_shift_op = true
resharper_space_around_stmt_colon = true
resharper_space_around_ternary_operator = true
resharper_space_before_array_rank_parentheses = false
resharper_space_before_attribute_target_colon = false
resharper_space_before_checked_parentheses = false
resharper_space_before_colon = false
resharper_space_before_colon_in_case = false
resharper_space_before_comma = false
resharper_space_before_default_parentheses = false
resharper_space_before_empty_invocation_parentheses = false
resharper_space_before_empty_method_parentheses = false
resharper_space_before_for_colon = true
resharper_space_before_initializer_braces = false
resharper_space_before_invocation_parentheses = false
resharper_space_before_label_colon = false
resharper_space_before_method_parentheses = false
resharper_space_before_nameof_parentheses = false
resharper_space_before_nullable_mark = false
resharper_space_before_open_square_brackets = false
resharper_space_before_pointer_asterik_declaration = false
resharper_space_before_ptr_in_data_member = false
resharper_space_before_ptr_in_data_members = true
resharper_space_before_ptr_in_method = false
resharper_space_before_self_closing = false
resharper_space_before_semicolon = false
resharper_space_before_semicolon_in_for_statement = false
resharper_space_before_singleline_accessorholder = true
resharper_space_before_sizeof_parentheses = false
resharper_space_before_template_args = false
resharper_space_before_template_params = true
resharper_space_before_ternary_colon = true
resharper_space_before_ternary_quest = true
resharper_space_before_trailing_comment = true
resharper_space_before_type_argument_angle = false
resharper_space_before_type_parameter_angle = false
resharper_space_before_type_parameter_constraint_colon = true
resharper_space_before_type_parameter_parentheses = true
resharper_space_before_typeof_parentheses = false
resharper_space_between_accessors_in_singleline_property = true
resharper_space_between_attribute_sections = true
resharper_space_between_closing_angle_brackets_in_template_args = false
resharper_space_between_keyword_and_expression = true
resharper_space_between_keyword_and_type = true
resharper_space_between_method_call_empty_parameter_list_parentheses = false
resharper_space_between_method_call_name_and_opening_parenthesis = false
resharper_space_between_method_call_parameter_list_parentheses = false
resharper_space_between_method_declaration_empty_parameter_list_parentheses = false
resharper_space_between_method_declaration_name_and_open_parenthesis = false
resharper_space_between_method_declaration_parameter_list_parentheses = false
resharper_space_between_parentheses_of_control_flow_statements = false
resharper_space_between_square_brackets = false
resharper_space_between_typecast_parentheses = false
resharper_space_in_singleline_accessorholder = true
resharper_space_in_singleline_anonymous_method = true
resharper_space_in_singleline_method = true
resharper_space_near_postfix_and_prefix_op = false
resharper_space_within_array_initialization_braces = false
resharper_space_within_array_rank_empty_parentheses = false
resharper_space_within_array_rank_parentheses = false
resharper_space_within_attribute_angles = false
resharper_space_within_checked_parentheses = false
resharper_space_within_default_parentheses = false
resharper_space_within_empty_braces = true
resharper_space_within_empty_initializer_braces = true
resharper_space_within_empty_invocation_parentheses = false
resharper_space_within_empty_method_parentheses = false
resharper_space_within_empty_template_params = false
resharper_space_within_expression_parentheses = false
resharper_space_within_initializer_braces = false
resharper_space_within_invocation_parentheses = false
resharper_space_within_method_parentheses = false
resharper_space_within_nameof_parentheses = false
resharper_space_within_parentheses = false
resharper_space_within_single_line_array_initializer_braces = true
resharper_space_within_sizeof_parentheses = false
resharper_space_within_template_args = false
resharper_space_within_template_params = false
resharper_space_within_tuple_parentheses = false
resharper_space_within_type_argument_angles = false
resharper_space_within_type_parameter_angles = false
resharper_space_within_type_parameter_parentheses = false
resharper_space_within_typeof_parentheses = false
resharper_spaces_around_eq_in_attribute = false
resharper_spaces_around_eq_in_pi_attribute = false
resharper_spaces_inside_tags = false
resharper_special_else_if_treatment = true
resharper_static_members_qualify_members = none
resharper_static_members_qualify_with = declared_type
resharper_stick_comment = true
resharper_toplevel_function_declaration_return_type_style = do_not_change
resharper_toplevel_function_definition_return_type_style = do_not_change
resharper_trailing_comma_in_multiline_lists = true
resharper_trailing_comma_in_singleline_lists = false
resharper_use_continuous_indent_inside_initializer_braces = true
resharper_use_continuous_indent_inside_parens = true
resharper_use_heuristics_for_body_style = true
resharper_wrap_after_declaration_lpar = false
resharper_wrap_after_dot_in_method_calls = false
resharper_wrap_after_invocation_lpar = false
resharper_wrap_arguments_style = wrap_if_long
resharper_wrap_around_elements = true
resharper_wrap_array_initializer_style = chop_if_long
resharper_wrap_base_clause_style = wrap_if_long
resharper_wrap_before_arrow_with_expressions = false
resharper_wrap_before_binary_opsign = true
resharper_wrap_before_colon = false
resharper_wrap_before_comma = false
resharper_wrap_before_declaration_lpar = false
resharper_wrap_before_declaration_rpar = false
resharper_wrap_before_extends_colon = false
resharper_wrap_before_first_type_parameter_constraint = false
resharper_wrap_before_invocation_lpar = false
resharper_wrap_before_invocation_rpar = false
resharper_wrap_before_linq_expression = true
resharper_wrap_before_ternary_opsigns = true
resharper_wrap_before_type_parameter_langle = false
resharper_wrap_braced_init_list_style = wrap_if_long
resharper_wrap_chained_binary_expressions = chop_if_long
resharper_wrap_chained_method_calls = wrap_if_long
resharper_wrap_ctor_initializer_style = wrap_if_long
resharper_wrap_enum_declaration = chop_always
resharper_wrap_enumeration_style = chop_if_long
resharper_wrap_extends_list_style = wrap_if_long
resharper_wrap_for_stmt_header_style = chop_if_long
resharper_wrap_multiple_declaration_style = chop_if_long
resharper_wrap_multiple_type_parameter_constraints_style = chop_if_long
resharper_wrap_object_and_collection_initializer_style = chop_if_long
resharper_wrap_parameters_style = wrap_if_long
resharper_wrap_tags_and_pi = true
resharper_wrap_ternary_expr_style = chop_if_long
resharper_wrap_text = true
resharper_wrap_verbatim_interpolated_strings = no_wrap

# .NET Unnecessary Code Rules
# https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/unnecessary-code-rules
dotnet_code_quality_unused_parameters = non_public
dotnet_diagnostic.ide0001.severity = warning
dotnet_diagnostic.ide0002.severity = warning
dotnet_diagnostic.ide0004.severity = warning
dotnet_diagnostic.ide0005.severity = warning
dotnet_diagnostic.ide0051.severity = warning
dotnet_diagnostic.ide0052.severity = warning
dotnet_diagnostic.ide0059.severity = warning
dotnet_diagnostic.ide0060.severity = warning
dotnet_diagnostic.ide0079.severity = warning
dotnet_diagnostic.ide0080.severity = warning
dotnet_diagnostic.ide0100.severity = warning
dotnet_diagnostic.ide0110.severity = warning
# some code requires setters for reflections to work
dotnet_diagnostic.IDE0032.severity = none

# StyleCop
# https://github.com/DotNetAnalyzers/StyleCopAnalyzers/blob/master/DOCUMENTATION.md
# https://github.com/DotNetAnalyzers/StyleCopAnalyzers/blob/master/StyleCop.Analyzers/StyleCop.Analyzers.CodeFixes/rulesetsst/StyleCopAnalyzersDefault.ruleset
dotnet_diagnostic.sa1000.severity = none
dotnet_diagnostic.sa1009.severity = none
dotnet_diagnostic.sa1013.severity = none
dotnet_diagnostic.sa1101.severity = none
dotnet_diagnostic.sa1111.severity = none
dotnet_diagnostic.sa1118.severity = none
dotnet_diagnostic.sa1127.severity = none
dotnet_diagnostic.sa1128.severity = none
dotnet_diagnostic.sa1134.severity = none
dotnet_diagnostic.sa1201.severity = none
dotnet_diagnostic.sa1300.severity = none
dotnet_diagnostic.sa1306.severity = none
dotnet_diagnostic.sa1310.severity = none
dotnet_diagnostic.sa1401.severity = none
dotnet_diagnostic.sa1402.severity = none
dotnet_diagnostic.sa1404.severity = none
dotnet_diagnostic.sa1414.severity = none
dotnet_diagnostic.sa1502.severity = none
dotnet_diagnostic.sa1503.severity = none
dotnet_diagnostic.sa1516.severity = none
dotnet_diagnostic.sa1519.severity = none
dotnet_diagnostic.sa1600.severity = none
dotnet_diagnostic.sa1601.severity = none
dotnet_diagnostic.sa1602.severity = none
dotnet_diagnostic.sa1611.severity = none
dotnet_diagnostic.sa1614.severity = none
dotnet_diagnostic.sa1615.severity = none
dotnet_diagnostic.sa1616.severity = none
dotnet_diagnostic.sa1618.severity = none
dotnet_diagnostic.sa1623.severity = none
dotnet_diagnostic.sa1629.severity = none
dotnet_diagnostic.sa1632.severity = none
dotnet_diagnostic.sa1633.severity = none

# TODO: enable?
dotnet_diagnostic.sa1202.severity = none
dotnet_diagnostic.sa1204.severity = none
dotnet_diagnostic.sa1500.severity = none

# Roslynator
# https://github.com/JosefPihrt/Roslynator/blob/main/src/Analyzers/README.md
dotnet_diagnostic.rcs1001.severity = none
dotnet_diagnostic.rcs1194.severity = none

# ReSharper properties
resharper_csharp_align_multiline_calls_chain = true
resharper_csharp_blank_lines_around_invocable = 1
resharper_csharp_blank_lines_around_namespace = 1
resharper_csharp_blank_lines_around_single_line_invocable = 1
resharper_csharp_max_line_length = 300
resharper_csharp_stick_comment = false
resharper_max_primary_constructor_parameters_on_line = 5



# Microsoft .NET properties
csharp_new_line_before_members_in_object_initializers = false
csharp_preferred_modifier_order = public, private, protected, internal, new, abstract, virtual, sealed, override, static, readonly, extern, unsafe, volatile, async, file, required:suggestion
csharp_style_prefer_utf8_string_literals = true:suggestion
csharp_style_var_elsewhere = true:suggestion
csharp_style_var_for_built_in_types = true:suggestion
csharp_style_var_when_type_is_apparent = true:suggestion
dotnet_naming_rule.constants_rule_rule.import_to_resharper = True
dotnet_naming_rule.constants_rule_rule.resharper_description = constants_rule
dotnet_naming_rule.constants_rule_rule.resharper_guid = e11ae918-5e9f-4104-ad14-5ea33a5aa202
dotnet_naming_rule.constants_rule_rule.severity = warning
dotnet_naming_rule.constants_rule_rule.style = all_upper_style
dotnet_naming_rule.constants_rule_rule.symbols = constants_rule_symbols
dotnet_naming_rule.private_constants_rule_rule.import_to_resharper = True
dotnet_naming_rule.private_constants_rule_rule.resharper_description = private_constants_rule
dotnet_naming_rule.private_constants_rule_rule.resharper_guid = 5ee43fb6-4497-480d-a772-6cfaa718ec47
dotnet_naming_rule.private_constants_rule_rule.severity = warning
dotnet_naming_rule.private_constants_rule_rule.style = upper_camel_case_style
dotnet_naming_rule.private_constants_rule_rule.symbols = private_constants_rule_symbols
dotnet_naming_rule.private_instance_fields_override_rule.import_to_resharper = False
dotnet_naming_rule.private_instance_fields_override_rule.severity = warning
dotnet_naming_rule.private_instance_fields_override_rule.style = lower_camel_case_style
dotnet_naming_rule.private_instance_fields_override_rule.symbols = private_instance_fields_override_symbols
dotnet_naming_rule.private_instance_fields_rule.import_to_resharper = True
dotnet_naming_rule.private_instance_fields_rule.resharper_description = Instance fields (private)
dotnet_naming_rule.private_instance_fields_rule.resharper_guid = 4a98fdf6-7d98-4f5a-afeb-ea44ad98c70c
dotnet_naming_rule.private_instance_fields_rule.severity = warning
dotnet_naming_rule.private_instance_fields_rule.style = lower_camel_case_style
dotnet_naming_rule.private_instance_fields_rule.symbols = private_instance_fields_symbols
dotnet_naming_rule.private_instance_fields_rule_rule.import_to_resharper = True
dotnet_naming_rule.private_instance_fields_rule_rule.resharper_description = private_instance_fields_rule
dotnet_naming_rule.private_instance_fields_rule_rule.resharper_guid = 53386b3e-338a-43ab-9e8a-e98014206f5a
dotnet_naming_rule.private_instance_fields_rule_rule.severity = warning
dotnet_naming_rule.private_instance_fields_rule_rule.style = lower_camel_case_style
dotnet_naming_rule.private_instance_fields_rule_rule.symbols = private_instance_fields_rule_symbols
dotnet_naming_rule.private_static_fields_rule.import_to_resharper = True
dotnet_naming_rule.private_static_fields_rule.resharper_description = Static fields (private)
dotnet_naming_rule.private_static_fields_rule.resharper_guid = f9fce829-e6f4-4cb2-80f1-5497c44f51df
dotnet_naming_rule.private_static_fields_rule.severity = warning
dotnet_naming_rule.private_static_fields_rule.style = lower_camel_case_style
dotnet_naming_rule.private_static_fields_rule.symbols = private_static_fields_symbols
dotnet_naming_rule.private_static_fields_rule_rule.import_to_resharper = True
dotnet_naming_rule.private_static_fields_rule_rule.resharper_description = private_static_fields_rule
dotnet_naming_rule.private_static_fields_rule_rule.resharper_guid = 2c1d52cc-c7fa-49af-bb62-405b9c8e7cb5
dotnet_naming_rule.private_static_fields_rule_rule.severity = warning
dotnet_naming_rule.private_static_fields_rule_rule.style = lower_camel_case_style
dotnet_naming_rule.private_static_fields_rule_rule.symbols = private_static_fields_rule_symbols
dotnet_naming_rule.private_static_readonly_rule_rule.import_to_resharper = True
dotnet_naming_rule.private_static_readonly_rule_rule.resharper_description = private_static_readonly_rule
dotnet_naming_rule.private_static_readonly_rule_rule.resharper_guid = 4b9c9ff8-df0e-4af8-8a59-2977d972736f
dotnet_naming_rule.private_static_readonly_rule_rule.severity = warning
dotnet_naming_rule.private_static_readonly_rule_rule.style = upper_camel_case_style
dotnet_naming_rule.private_static_readonly_rule_rule.symbols = private_static_readonly_rule_symbols
dotnet_naming_rule.public_fields_rule_rule.import_to_resharper = True
dotnet_naming_rule.public_fields_rule_rule.resharper_description = public_fields_rule
dotnet_naming_rule.public_fields_rule_rule.resharper_guid = 5bffec7b-2188-4519-aed4-35353d537bc0
dotnet_naming_rule.public_fields_rule_rule.severity = warning
dotnet_naming_rule.public_fields_rule_rule.style = upper_camel_case_style
dotnet_naming_rule.public_fields_rule_rule.symbols = public_fields_rule_symbols
dotnet_naming_rule.static_readonly_rule_rule.import_to_resharper = True
dotnet_naming_rule.static_readonly_rule_rule.resharper_description = static_readonly_rule
dotnet_naming_rule.static_readonly_rule_rule.resharper_guid = 1deb46dc-9e9c-4653-b3e1-56d2d002ff5b
dotnet_naming_rule.static_readonly_rule_rule.severity = warning
dotnet_naming_rule.static_readonly_rule_rule.style = all_upper_style
dotnet_naming_rule.static_readonly_rule_rule.symbols = static_readonly_rule_symbols
dotnet_naming_rule.unity_serialized_field_rule.import_to_resharper = True
dotnet_naming_rule.unity_serialized_field_rule.resharper_description = Unity serialized field
dotnet_naming_rule.unity_serialized_field_rule.resharper_guid = 5f0fdb63-c892-4d2c-9324-15c80b22a7ef
dotnet_naming_rule.unity_serialized_field_rule.severity = warning
dotnet_naming_rule.unity_serialized_field_rule.style = lower_camel_case_style
dotnet_naming_rule.unity_serialized_field_rule.symbols = unity_serialized_field_symbols
dotnet_naming_style.all_upper_style.capitalization = all_upper
dotnet_naming_style.all_upper_style.word_separator = _
dotnet_naming_style.lower_camel_case_style.capitalization = camel_case
dotnet_naming_style.upper_camel_case_style.capitalization = pascal_case
dotnet_naming_symbols.constants_rule_symbols.applicable_accessibilities = public,internal,protected,protected_internal,private_protected
dotnet_naming_symbols.constants_rule_symbols.applicable_kinds = field
dotnet_naming_symbols.constants_rule_symbols.required_modifiers = const
dotnet_naming_symbols.constants_rule_symbols.resharper_applicable_kinds = constant_field
dotnet_naming_symbols.constants_rule_symbols.resharper_required_modifiers = any
dotnet_naming_symbols.private_constants_rule_symbols.applicable_accessibilities = private
dotnet_naming_symbols.private_constants_rule_symbols.applicable_kinds = field
dotnet_naming_symbols.private_constants_rule_symbols.required_modifiers = const
dotnet_naming_symbols.private_constants_rule_symbols.resharper_applicable_kinds = constant_field
dotnet_naming_symbols.private_constants_rule_symbols.resharper_required_modifiers = any
dotnet_naming_symbols.private_instance_fields_override_symbols.applicable_accessibilities = private
dotnet_naming_symbols.private_instance_fields_override_symbols.applicable_kinds = field
dotnet_naming_symbols.private_instance_fields_override_symbols.required_modifiers = static
dotnet_naming_symbols.private_instance_fields_rule_symbols.applicable_accessibilities = private
dotnet_naming_symbols.private_instance_fields_rule_symbols.applicable_kinds = field
dotnet_naming_symbols.private_instance_fields_rule_symbols.resharper_applicable_kinds = any_field
dotnet_naming_symbols.private_instance_fields_rule_symbols.resharper_required_modifiers = any
dotnet_naming_symbols.private_instance_fields_symbols.applicable_accessibilities = private
dotnet_naming_symbols.private_instance_fields_symbols.applicable_kinds = field
dotnet_naming_symbols.private_instance_fields_symbols.resharper_applicable_kinds = field,readonly_field
dotnet_naming_symbols.private_instance_fields_symbols.resharper_required_modifiers = instance
dotnet_naming_symbols.private_static_fields_rule_symbols.applicable_accessibilities = private
dotnet_naming_symbols.private_static_fields_rule_symbols.applicable_kinds = field
dotnet_naming_symbols.private_static_fields_rule_symbols.required_modifiers = static
dotnet_naming_symbols.private_static_fields_rule_symbols.resharper_applicable_kinds = any_field
dotnet_naming_symbols.private_static_fields_rule_symbols.resharper_required_modifiers = static
dotnet_naming_symbols.private_static_fields_symbols.applicable_accessibilities = private
dotnet_naming_symbols.private_static_fields_symbols.applicable_kinds = field
dotnet_naming_symbols.private_static_fields_symbols.required_modifiers = static
dotnet_naming_symbols.private_static_fields_symbols.resharper_applicable_kinds = field
dotnet_naming_symbols.private_static_fields_symbols.resharper_required_modifiers = static
dotnet_naming_symbols.private_static_readonly_rule_symbols.applicable_accessibilities = private
dotnet_naming_symbols.private_static_readonly_rule_symbols.applicable_kinds = field
dotnet_naming_symbols.private_static_readonly_rule_symbols.required_modifiers = readonly,static
dotnet_naming_symbols.private_static_readonly_rule_symbols.resharper_applicable_kinds = readonly_field
dotnet_naming_symbols.private_static_readonly_rule_symbols.resharper_required_modifiers = static
dotnet_naming_symbols.public_fields_rule_symbols.applicable_accessibilities = public,internal,protected,protected_internal,private_protected
dotnet_naming_symbols.public_fields_rule_symbols.applicable_kinds = field
dotnet_naming_symbols.public_fields_rule_symbols.resharper_applicable_kinds = any_field
dotnet_naming_symbols.public_fields_rule_symbols.resharper_required_modifiers = any
dotnet_naming_symbols.static_readonly_rule_symbols.applicable_accessibilities = public,internal,protected,protected_internal,private_protected
dotnet_naming_symbols.static_readonly_rule_symbols.applicable_kinds = field
dotnet_naming_symbols.static_readonly_rule_symbols.required_modifiers = readonly,static
dotnet_naming_symbols.static_readonly_rule_symbols.resharper_applicable_kinds = readonly_field
dotnet_naming_symbols.static_readonly_rule_symbols.resharper_required_modifiers = static
dotnet_naming_symbols.unity_serialized_field_symbols.applicable_accessibilities = *
dotnet_naming_symbols.unity_serialized_field_symbols.applicable_kinds = 
dotnet_naming_symbols.unity_serialized_field_symbols.resharper_applicable_kinds = unity_serialised_field
dotnet_naming_symbols.unity_serialized_field_symbols.resharper_required_modifiers = instance
dotnet_style_parentheses_in_arithmetic_binary_operators = never_if_unnecessary:none
dotnet_style_parentheses_in_other_binary_operators = never_if_unnecessary:none
dotnet_style_parentheses_in_relational_binary_operators = never_if_unnecessary:none
dotnet_style_predefined_type_for_locals_parameters_members = true:suggestion
dotnet_style_predefined_type_for_member_access = true:suggestion
dotnet_style_qualification_for_event = false:suggestion
dotnet_style_qualification_for_field = false:suggestion
dotnet_style_qualification_for_method = false:suggestion
dotnet_style_qualification_for_property = false:suggestion
dotnet_style_require_accessibility_modifiers = for_non_interface_members:suggestion

# ReSharper properties
resharper_align_linq_query = true
resharper_align_multiline_argument = false
resharper_align_multiline_expression = false
resharper_align_multiline_extends_list = false
resharper_align_multiline_for_stmt = false
resharper_align_multiline_parameter = true
resharper_align_multiple_declaration = false
resharper_apply_auto_detected_rules = false
resharper_attribute_indent = align_by_first_attribute
resharper_blank_lines_after_control_transfer_statements = 1
resharper_blank_lines_after_multiline_statements = 1
resharper_blank_lines_after_start_comment = 0
resharper_blank_lines_around_auto_property = 0
resharper_blank_lines_around_field = 0
resharper_blank_lines_around_single_line_local_method = 1
resharper_blank_lines_before_block_statements = 1
resharper_blank_lines_before_control_transfer_statements = 1
resharper_blank_lines_before_multiline_statements = 1
resharper_blank_lines_before_single_line_comment = 1
resharper_braces_for_for = required
resharper_braces_for_foreach = required
resharper_braces_for_ifelse = required
resharper_braces_for_while = required
resharper_cpp_align_multiline_calls_chain = false
resharper_cpp_blank_lines_around_namespace = 0
resharper_cpp_case_block_braces = next_line
resharper_csharp_align_multiline_calls_chain = true
resharper_csharp_blank_lines_around_single_line_invocable = 1
resharper_csharp_stick_comment = false
resharper_empty_block_style = together_same_line
resharper_enforce_line_ending_style = true
resharper_indent_child_elements = RemoveIndent
resharper_indent_text = RemoveIndent
resharper_keep_blank_lines_in_code = 1
resharper_keep_blank_lines_in_declarations = 1
resharper_keep_existing_attribute_arrangement = true
resharper_keep_existing_declaration_block_arrangement = true
resharper_keep_existing_embedded_block_arrangement = true
resharper_keep_existing_enum_arrangement = false
resharper_max_attribute_length_for_same_line = 70
resharper_max_blank_lines_between_tags = 1
resharper_max_primary_constructor_parameters_on_line = 5
resharper_new_line_before_while = false
resharper_outdent_binary_ops = true
resharper_pi_attribute_style = do_not_touch
resharper_place_accessorholder_attribute_on_same_line = false
resharper_place_linq_into_on_new_line = false
resharper_place_method_attribute_on_same_line = true
resharper_prefer_qualified_reference = false
resharper_remove_blank_lines_near_braces_in_declarations = false
resharper_show_autodetect_configure_formatting_tip = false
resharper_space_after_unary_operator = false
resharper_space_before_self_closing = false
resharper_space_within_empty_initializer_braces = true
resharper_trailing_comma_in_multiline_lists = true
resharper_use_indent_from_vs = false
resharper_vb_blank_lines_around_invocable = 0
resharper_vb_blank_lines_around_namespace = 0
resharper_wrap_array_initializer_style = chop_if_long
resharper_wrap_before_binary_opsign = true
resharper_wrap_before_linq_expression = true
resharper_wrap_chained_binary_expressions = chop_if_long
resharper_wrap_lines = true
resharper_wrap_tags_and_pi = true
resharper_wrap_text = true

# ReSharper inspection severities
resharper_access_to_static_member_via_derived_type_highlighting = hint
resharper_mvc_action_not_resolved_highlighting = warning
resharper_mvc_area_not_resolved_highlighting = warning
resharper_mvc_controller_not_resolved_highlighting = warning
resharper_mvc_masterpage_not_resolved_highlighting = warning
resharper_mvc_partial_view_not_resolved_highlighting = warning
resharper_mvc_template_not_resolved_highlighting = warning
resharper_mvc_view_component_not_resolved_highlighting = warning
resharper_mvc_view_component_view_not_resolved_highlighting = warning
resharper_mvc_view_not_resolved_highlighting = warning
resharper_razor_assembly_not_resolved_highlighting = warning
resharper_web_config_module_not_resolved_highlighting = warning
resharper_web_config_type_not_resolved_highlighting = warning
resharper_web_config_wrong_module_highlighting = warning

