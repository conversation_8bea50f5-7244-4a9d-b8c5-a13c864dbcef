using Game.AI;
using UnityEditor;
using UnityEditor.EditorTools;
using UnityEditor.SceneManagement;
using UnityEngine;

namespace GameEditor.LevelDesign
{
    [CustomEditor(typeof(SpawnPointSetup))]
    public class SpawnPointSetupEditor : Editor
    {
        private SpawnPointSetup spawnPointSetup;

        private void OnEnable()
        {
            spawnPointSetup = (SpawnPointSetup)target;
        }

        public override void OnInspectorGUI()
        {
            // Draw default inspector
            DrawDefaultInspector();

            GUILayout.Space(10);

            // Waypoint editing section
            GUILayout.BeginVertical("box");
            GUILayout.Label("Spawn Point Setup", EditorStyles.boldLabel);

            if (PrefabStageUtility.GetCurrentPrefabStage() != null)
            {
                GUILayout.Label("Instructions:", EditorStyles.label);
                GUILayout.Label("1. Activate the Waypoint Editor Tool", EditorStyles.helpBox);
                GUILayout.Label("2. Hold Ctrl + Click to create spawn point", EditorStyles.helpBox);
                GUILayout.Label("3. Click to add waypoints", EditorStyles.helpBox);
                GUILayout.Label("4. Press Escape to finish path", EditorStyles.helpBox);

                GUILayout.Space(5);

                if (GUILayout.Button("Activate Waypoint Editor Tool"))
                {
                    ToolManager.SetActiveTool<WaypointEditorTool>();
                }

                GUILayout.Space(5);

                // Show spawn points info
                var validSpawnPoints = spawnPointSetup.GetValidSpawnPoints();
                GUILayout.Label($"Total Spawn Points: {spawnPointSetup.SpawnPointCount}", EditorStyles.label);
                GUILayout.Label($"Valid Spawn Points: {validSpawnPoints.Count}", EditorStyles.label);

                if (spawnPointSetup.SpawnPointCount > 0)
                {
                    GUILayout.Space(5);
                    GUILayout.Label("Spawn Points:", EditorStyles.boldLabel);

                    for (var i = 0; i < spawnPointSetup.SpawnPointCount; i++)
                    {
                        var spawnPoint = spawnPointSetup.GetSpawnPoint(i);

                        if (spawnPoint != null)
                        {
                            GUILayout.BeginHorizontal();

                            // Status indicator
                            var statusColor = spawnPoint.WaypointCount > 0 ? Color.green : Color.red;
                            var statusText = spawnPoint.WaypointCount > 0 ? "✓" : "✗";

                            var oldColor = GUI.color;
                            GUI.color = statusColor;
                            GUILayout.Label(statusText, GUILayout.Width(20));
                            GUI.color = oldColor;

                            GUILayout.Label($"{spawnPoint.name}: {spawnPoint.WaypointCount} waypoints");

                            if (GUILayout.Button("Select", GUILayout.Width(60)))
                            {
                                Selection.activeGameObject = spawnPoint.gameObject;
                                SceneView.FrameLastActiveSceneView();
                            }

                            if (GUILayout.Button("Clear", GUILayout.Width(60)))
                            {
                                if (EditorUtility.DisplayDialog("Clear Waypoints",
                                        $"Are you sure you want to clear all waypoints for {spawnPoint.name}?",
                                        "Yes", "No"))
                                {
                                    spawnPoint.ClearWaypoints();
                                    EditorUtility.SetDirty(spawnPoint);
                                }
                            }

                            if (GUILayout.Button("Delete", GUILayout.Width(60)))
                            {
                                if (EditorUtility.DisplayDialog("Delete Spawn Point",
                                        $"Are you sure you want to delete {spawnPoint.name}?",
                                        "Yes", "No"))
                                {
                                    spawnPointSetup.RemoveSpawnPoint(spawnPoint);
                                    DestroyImmediate(spawnPoint.gameObject);
                                    EditorUtility.SetDirty(spawnPointSetup);

                                    break;
                                }
                            }

                            GUILayout.EndHorizontal();
                        }
                    }

                    GUILayout.Space(5);

                    GUILayout.BeginHorizontal();

                    if (GUILayout.Button("Clear All Waypoints"))
                    {
                        if (EditorUtility.DisplayDialog("Clear All Waypoints",
                                "Are you sure you want to clear all waypoints from all spawn points?",
                                "Yes", "No"))
                        {
                            ClearAllWaypoints();
                        }
                    }

                    if (GUILayout.Button("Delete All Spawn Points"))
                    {
                        if (EditorUtility.DisplayDialog("Delete All Spawn Points",
                                "Are you sure you want to delete all spawn points? This cannot be undone.",
                                "Yes", "No"))
                        {
                            spawnPointSetup.ClearAllSpawnPoints();
                            EditorUtility.SetDirty(spawnPointSetup);
                        }
                    }

                    GUILayout.EndHorizontal();
                }

                GUILayout.Space(10);

                // Validation section
                GUILayout.BeginVertical("helpBox");
                GUILayout.Label("Validation", EditorStyles.boldLabel);

                if (validSpawnPoints.Count == 0)
                {
                    GUILayout.Label("⚠ No valid spawn points (spawn points need at least 1 waypoint)", EditorStyles.helpBox);
                }
                else
                {
                    GUILayout.Label($"✓ {validSpawnPoints.Count} spawn points ready for use", EditorStyles.helpBox);
                }

                GUILayout.EndVertical();
            }
            else
            {
                GUILayout.Label("Enter Prefab Mode to edit spawn points", EditorStyles.helpBox);

                if (GUILayout.Button("Open Prefab for Editing"))
                {
                    AssetDatabase.OpenAsset(spawnPointSetup.gameObject);
                }
            }

            GUILayout.EndVertical();
        }

        private void ClearAllWaypoints()
        {
            for (var i = 0; i < spawnPointSetup.SpawnPointCount; i++)
            {
                var spawnPoint = spawnPointSetup.GetSpawnPoint(i);

                if (spawnPoint != null)
                {
                    spawnPoint.ClearWaypoints();
                    EditorUtility.SetDirty(spawnPoint);
                }
            }
        }
    }
}