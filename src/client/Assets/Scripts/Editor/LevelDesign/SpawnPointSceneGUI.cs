using Game.AI;
using UnityEditor;
using UnityEngine;

namespace GameEditor.LevelDesign
{
    [CustomEditor(typeof(SpawnPoint))]
    public class SpawnPointEditor : Editor
    {
        private SpawnPoint spawnPoint;

        private void OnEnable()
        {
            spawnPoint = (SpawnPoint)target;
        }

        public override void OnInspectorGUI()
        {
            DrawDefaultInspector();

            GUILayout.Space(10);

            GUILayout.BeginVertical("box");
            GUILayout.Label("Waypoint Management", EditorStyles.boldLabel);

            GUILayout.Label($"Waypoints: {spawnPoint.WaypointCount}");

            if (GUILayout.Button("Clear All Waypoints"))
            {
                if (EditorUtility.DisplayDialog("Clear Waypoints",
                        "Are you sure you want to clear all waypoints?",
                        "Yes", "No"))
                {
                    spawnPoint.ClearWaypoints();
                    EditorUtility.SetDirty(spawnPoint);
                }
            }

            GUILayout.EndVertical();
        }

        private void OnSceneGUI()
        {
            if (spawnPoint == null)
            {
                return;
            }

            // Draw spawn point handle
            EditorGUI.BeginChangeCheck();
            var newSpawnPos = Handles.PositionHandle(spawnPoint.transform.position, spawnPoint.transform.rotation);

            if (EditorGUI.EndChangeCheck())
            {
                Undo.RecordObject(spawnPoint.transform, "Move Spawn Point");
                spawnPoint.transform.position = newSpawnPos;
            }

            // Draw waypoint handles
            for (var i = 0; i < spawnPoint.WaypointCount; i++)
            {
                spawnPoint.GetWaypoint(i, out var waypoint);

                // Draw waypoint handle
                EditorGUI.BeginChangeCheck();
                var newWaypointPos = Handles.PositionHandle(waypoint, Quaternion.identity);

                if (EditorGUI.EndChangeCheck())
                {
                    Undo.RecordObject(spawnPoint, "Move Waypoint");
                    spawnPoint.Waypoints[i] = newWaypointPos;
                    EditorUtility.SetDirty(spawnPoint);
                }

                // Draw waypoint label
                Handles.Label(waypoint + Vector3.up * 0.5f, $"WP {i}");

                // Draw delete button
                if (Handles.Button(waypoint + Vector3.up * 1f, Quaternion.identity, 0.2f, 0.2f, Handles.SphereHandleCap))
                {
                    if (EditorUtility.DisplayDialog("Delete Waypoint",
                            $"Delete waypoint {i}?",
                            "Yes", "No"))
                    {
                        Undo.RecordObject(spawnPoint, "Delete Waypoint");
                        spawnPoint.Waypoints.RemoveAt(i);
                        EditorUtility.SetDirty(spawnPoint);

                        break;
                    }
                }
            }

            // Draw path lines
            if (spawnPoint.WaypointCount > 0)
            {
                Handles.color = Color.yellow;
                var previousPoint = spawnPoint.transform.position;

                for (var i = 0; i < spawnPoint.WaypointCount; i++)
                {
                    spawnPoint.GetWaypoint(i, out var currentPoint);
                    Handles.DrawLine(previousPoint, currentPoint);
                    previousPoint = currentPoint;
                }
            }
        }
    }
}