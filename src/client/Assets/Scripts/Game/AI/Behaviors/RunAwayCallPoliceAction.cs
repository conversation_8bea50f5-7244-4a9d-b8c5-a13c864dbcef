using System;
using Game.AI;
using Unity.Behavior;
using Unity.Properties;
using UnityEngine;
using Action = Unity.Behavior.Action;

[Serializable] [GeneratePropertyBag]
[NodeDescription("RunAwayCallPolice", story: "[Agent] keeps running away from [Player] while calling Police", category: "Action", id: "40ecc5c62ed3d4b20d7355d97ce6c9fb")]
public class RunAwayCallPoliceAction : Action
{
    [SerializeReference] public BlackboardVariable<GameObject> Agent;
    [SerializeReference] public BlackboardVariable<GameObject> Player;
    private readonly float distanceToRunAway = 10;

    private WandererBehavior wandererBehavior;

    protected override Status OnStart()
    {
        var agentValue = Agent.Value;

        if (agentValue == null)
        {
            return Status.Failure;
        }

        wandererBehavior = agentValue.GetComponent<WandererBehavior>();

        wandererBehavior.Navigate();
        wandererBehavior.SetRunning(true);

        return Status.Running;
    }

    protected override Status OnUpdate()
    {
        if (Vector3.Distance(Agent.Value.transform.position, Player.Value.transform.position) > distanceToRunAway)
        {
            return Status.Success;
        }

        return Status.Running;
    }

    protected override void OnEnd()
    {
        wandererBehavior.SetRunning(false);
        wandererBehavior.PauseWaypointNavigation();
    }
}