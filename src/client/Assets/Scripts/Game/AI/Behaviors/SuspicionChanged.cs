using System;
using Unity.Behavior;
using UnityEngine;
using Unity.Properties;

#if UNITY_EDITOR
[CreateAssetMenu(menuName = "Behavior/Event Channels/SuspicionChanged")]
#endif
[Serializable, GeneratePropertyBag]
[EventChannelDescription(name: "SuspicionChanged", message: "<PERSON><PERSON><PERSON> [suspicion] changed to [newValue]", category: "Events", id: "9ea05861e9883c51692ad2cd7ee8e0ea")]
public partial class SuspicionChanged : EventChannelBase
{
    public delegate void SuspicionChangedEventHandler(float suspicion, float newValue);
    public event SuspicionChangedEventHandler Event; 

    public void SendEventMessage(float suspicion, float newValue)
    {
        Event?.Invoke(suspicion, newValue);
    }

    public override void SendEventMessage(BlackboardVariable[] messageData)
    {
        BlackboardVariable<float> suspicionBlackboardVariable = messageData[0] as BlackboardVariable<float>;
        var suspicion = suspicionBlackboardVariable != null ? suspicionBlackboardVariable.Value : default(float);

        BlackboardVariable<float> newValueBlackboardVariable = messageData[1] as BlackboardVariable<float>;
        var newValue = newValueBlackboardVariable != null ? newValueBlackboardVariable.Value : default(float);

        Event?.Invoke(suspicion, newValue);
    }

    public override Delegate CreateEventHandler(BlackboardVariable[] vars, System.Action callback)
    {
        SuspicionChangedEventHandler del = (suspicion, newValue) =>
        {
            BlackboardVariable<float> var0 = vars[0] as BlackboardVariable<float>;
            if(var0 != null)
                var0.Value = suspicion;

            BlackboardVariable<float> var1 = vars[1] as BlackboardVariable<float>;
            if(var1 != null)
                var1.Value = newValue;

            callback();
        };
        return del;
    }

    public override void RegisterListener(Delegate del)
    {
        Event += del as SuspicionChangedEventHandler;
    }

    public override void UnregisterListener(Delegate del)
    {
        Event -= del as SuspicionChangedEventHandler;
    }
}

