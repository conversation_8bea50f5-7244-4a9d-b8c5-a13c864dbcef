using System;
using Game.AI;
using Unity.Behavior;
using Unity.Properties;
using UnityEngine;
using Action = Unity.Behavior.Action;

[Serializable] [GeneratePropertyBag]
[NodeDescription("ReactToThrowable", story: "[Agent] stops and looks at [LookTarget] for [ActionDuration] seconds", category: "Action", id: "1336adbf73e44db14f016963c5dd8818")]
public class ReactToThrowableAction : Action
{
    [SerializeReference] public BlackboardVariable<float> ActionDuration;
    [SerializeReference] public BlackboardVariable<GameObject> Agent;
    [SerializeReference] public BlackboardVariable<Vector3> LookTarget;
    private float timeEnd;
    private WandererBehavior? wandererBehavior;

    protected override Status OnStart()
    {
        var agentObject = Agent.Value;

        if (agentObject == null)
        {
            return Status.Failure;
        }

        timeEnd = Time.time + ActionDuration.Value;
        wandererBehavior = agentObject.GetComponent<WandererBehavior>();
        wandererBehavior.PauseWaypointNavigation();

        return Status.Running;
    }

    protected override Status OnUpdate()
    {
        if (Time.time < timeEnd)
        {
            var target = LookTarget.Value;
            var transform = Agent.Value.transform;

            // Get the direction from this object to the target
            var directionToTarget = target - transform.position;

            directionToTarget.y = 0;

            // Create a rotation that looks at the target while keeping the up direction fixed
            var rotation = Quaternion.LookRotation(directionToTarget);

            transform.rotation = rotation;

            return Status.Running;
        }

        return Status.Success;
    }

    protected override void OnEnd() { }
}