using System;
using Game.AI;
using Unity.Behavior;
using Unity.Properties;
using UnityEngine;
using Action = Unity.Behavior.Action;

[Serializable] [GeneratePropertyBag]
[NodeDescription("Wander", story: "[Agent] wanders around within [radius] units from [spawnPoint]", category: "Action", id: "76329abe85ef54425af55018374bc7b7")]
public class WanderAction : Action
{
    [SerializeReference] public BlackboardVariable<GameObject> Agent;
    [SerializeReference] public BlackboardVariable<float> Radius;
    [SerializeReference] public BlackboardVariable<Vector3> SpawnPoint;

    private WandererBehavior wandererBehavior;

    protected override Status OnStart()
    {
        var agentObject = Agent.Value;

        if (agentObject == null)
        {
            return Status.Failure;
        }

        wandererBehavior = agentObject.GetComponent<WandererBehavior>();
        wandererBehavior.Navigate();

        return Status.Running;
    }

    protected override Status OnUpdate()
    {
        return Status.Running;
    }

    protected override void OnEnd()
    {
        wandererBehavior.PauseWaypointNavigation();
    }
}