using System;
using R3;
using UnityEngine;

namespace Game.AI.Sensors
{
    public class PresenceAccumulatingSensor : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ISuspicionSensor
    {
        [SerializeField] private float accumulationLossPerSecond;
        [SerializeField] private float accumulationPerSecond;
        [SerializeField] private float registerTimesPerSecond = 10;

        public Observable<float> ModifierStream => reactiveProperty;

        public bool PlayerIn;
        private readonly ReactiveProperty<float> reactiveProperty = new();

        private readonly SerialDisposable subscription = new();
        private TimeSpan delta;
        private float maxStayAccumulationValue;

        private void Awake()
        {
            delta = TimeSpan.FromSeconds(1f / registerTimesPerSecond);
        }

        private void OnDisable()
        {
            //make the value clamp and pause when reaction is playing
            subscription.Dispose();
        }

        private void OnTriggerEnter(Collider other)
        {
            if (!other.gameObject.CompareTag("Player")) { return; }

            PlayerIn = true;

            subscription.Disposable = Observable.Interval(delta)
                                                .Subscribe(IncreaseSuspicion);
        }

        private void OnTriggerExit(Collider other)
        {
            if (!other.gameObject.CompareTag("Player")) { return; }

            PlayerIn = false;

            subscription.Disposable = Observable.Interval(delta)
                                                .Delay(TimeSpan.FromSeconds(2), UnityTimeProvider.TimeUpdate)
                                                .Subscribe(DecreaseSuspicion);
        }

        private void IncreaseSuspicion(Unit _)
        {
            reactiveProperty.OnNext(accumulationPerSecond / registerTimesPerSecond);
        }

        private void DecreaseSuspicion(Unit _)
        {
            reactiveProperty.OnNext(-accumulationLossPerSecond / registerTimesPerSecond);
        }
    }
}