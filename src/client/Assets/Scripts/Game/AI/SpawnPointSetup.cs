using System.Collections.Generic;
using UnityEngine;

namespace Game.AI
{
    [System.Serializable]
    public class SpawnPointSetup : MonoBehaviour
    {
        [SerializeField] private List<SpawnPoint> spawnPoints = new();

        [Header("Setup Info")]
        [SerializeField] private string setupName = "Default Setup";
        [TextArea(3, 5)]
        [SerializeField] private string description = "Describe this wanderer setup...";

        public List<SpawnPoint> SpawnPoints => spawnPoints;
        public string SetupName => setupName;
        public string Description => description;

        public int SpawnPointCount => spawnPoints.Count;

        private void OnDrawGizmos()
        {
            if (spawnPoints == null)
            {
                return;
            }

            // Draw connections between all spawn points to show they belong to this setup
            Gizmos.color = Color.cyan;

            for (var i = 0; i < spawnPoints.Count - 1; i++)
            {
                if (spawnPoints[i] != null && spawnPoints[i + 1] != null)
                {
                    var start = spawnPoints[i].transform.position + Vector3.up * 2f;
                    var end = spawnPoints[i + 1].transform.position + Vector3.up * 2f;
                    Gizmos.DrawLine(start, end);
                }
            }

            // Draw setup center
            if (spawnPoints.Count > 0)
            {
                Gizmos.color = Color.magenta;
                Gizmos.DrawWireCube(transform.position, Vector3.one * 0.5f);
            }
        }

        private void OnValidate()
        {
            ValidateSpawnPoints();
        }

        public void AddSpawnPoint(SpawnPoint spawnPoint)
        {
            if (spawnPoint != null && !spawnPoints.Contains(spawnPoint))
            {
                spawnPoints.Add(spawnPoint);
                spawnPoint.transform.SetParent(transform);
            }
        }

        public void ClearAllSpawnPoints()
        {
            // Remove all spawn point GameObjects
            for (var i = spawnPoints.Count - 1; i >= 0; i--)
            {
                if (spawnPoints[i] != null)
                {
#if UNITY_EDITOR
                    if (Application.isPlaying)
                    {
                        Destroy(spawnPoints[i].gameObject);
                    }
                    else
                    {
                        DestroyImmediate(spawnPoints[i].gameObject);
                    }
#else
                    Destroy(spawnPoints[i].gameObject);
#endif
                }
            }

            spawnPoints.Clear();
        }

        public SpawnPoint GetSpawnPoint(int index)
        {
            if (index >= 0 && index < spawnPoints.Count)
            {
                return spawnPoints[index];
            }

            return null;
        }

        /// <summary>
        /// Get all spawn points that have at least one waypoint
        /// </summary>
        public List<SpawnPoint> GetValidSpawnPoints()
        {
            var validPoints = new List<SpawnPoint>();

            foreach (var spawnPoint in spawnPoints)
            {
                if (spawnPoint != null && spawnPoint.WaypointCount > 0)
                {
                    validPoints.Add(spawnPoint);
                }
            }

            return validPoints;
        }

        public void RemoveSpawnPoint(SpawnPoint spawnPoint)
        {
            if (spawnPoints.Contains(spawnPoint))
            {
                spawnPoints.Remove(spawnPoint);
            }
        }

        /// <summary>
        /// Validate and clean up any null references
        /// </summary>
        public void ValidateSpawnPoints()
        {
            for (var i = spawnPoints.Count - 1; i >= 0; i--)
            {
                if (spawnPoints[i] == null)
                {
                    spawnPoints.RemoveAt(i);
                }
            }
        }
    }
}