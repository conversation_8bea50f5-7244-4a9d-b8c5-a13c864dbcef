using System.Collections.Generic;
using Core.Extensions;
using Game.Logic.Level;
using MessagePipe;
using Microsoft.Extensions.Logging;
using UnityEngine;
using UnityEngine.AI;
using VContainer;
using VContainer.Unity;
using ILogger = Microsoft.Extensions.Logging.ILogger;

namespace Game.AI
{
    public class WandererService : MonoBehaviour
    {
        [SerializeField] private GameObject wanderer;
        [SerializeField] private GameObject[] skins;

        private ISubscriber<LevelLoadedSignal> levelLoaded;
        private LevelObjects levelObjects;
        private ILogger logger;
        private IObjectResolver resolver;

        [Inject]
        internal void Inject(ISubscriber<LevelLoadedSignal> levelLoadedSignal,
                             LevelObjects levelObjects,
                             IObjectResolver resolver,
                             ILoggerFactory loggerFactory)
        {
            this.resolver = resolver;
            this.levelObjects = levelObjects;
            levelLoaded = levelLoadedSignal;
            logger = loggerFactory.CreateLogger(GetType());
        }

        private void Start()
        {
            DisposableBag.Create(
                levelLoaded.Subscribe(OnLevelLoaded)
            ).DisposeOnDestroy(this);
        }

        private void OnLevelLoaded(LevelLoadedSignal levelLoadedSignal)
        {
            SpawnWanderers(levelLoadedSignal.SpawnPointSetup.SpawnPoints);
        }

        private void SpawnWanderers(List<SpawnPoint> activeSpawnPoints)
        {
            if (activeSpawnPoints.Count == 0)
            {
                Debug.LogWarning("No spawn points available for WandererService. Either assign spawn points directly or assign a SpawnPointSetup prefab.");

                return;
            }

            for (var i = 0; i < activeSpawnPoints.Count; i++)
            {
                var spawnPoint = activeSpawnPoints[i];

                if (spawnPoint == null || spawnPoint.WaypointCount == 0)
                {
                    Debug.LogWarning($"Spawn point {spawnPoint?.name} has no waypoints defined.");

                    continue;
                }

                // Spawn wanderer at spawn point position
                var wandererInstance = resolver.Instantiate(wanderer, transform);
                var navMeshAgent = wandererInstance.GetComponent<NavMeshAgent>();
                navMeshAgent.enabled = false;
                wandererInstance.transform.position = spawnPoint.SpawnPosition;
                navMeshAgent.enabled = true;

                var wandererBehavior = wandererInstance.GetComponent<WandererBehavior>();

                if (wandererBehavior != null)
                {
                    wandererBehavior.SetSkin(i + 1, skins.GetRandom());
                    wandererBehavior.SetSpawnPoint(spawnPoint);
                    levelObjects.Wanderers.Add(wandererBehavior);
                }
                else
                {
                    Debug.LogError("Wanderer prefab is missing WandererBehavior component!");
                }
            }
        }
    }

}