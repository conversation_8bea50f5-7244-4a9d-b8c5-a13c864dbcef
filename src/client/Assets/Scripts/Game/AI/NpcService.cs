using System.Collections.Generic;
using Game.AI.Data;
using Game.Gameplay.ViewData;

namespace Game.AI
{
    public class NpcService
    {

        private class Npc
        {
            public readonly NpcDefinition Definition;
            public readonly uint Id;
            public readonly List<LootStorageEntry> Loot;

            public Npc(uint id, NpcDefinition definition)
            {
                Definition = definition;
                Id = id;
                Loot = new List<LootStorageEntry>();
            }
        }

        public struct NpcDefinition
        {
            public NpcType Type;
            public uint VisualId;
        }
    }
}