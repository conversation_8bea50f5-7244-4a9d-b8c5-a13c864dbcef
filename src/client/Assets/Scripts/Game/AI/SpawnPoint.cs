using System.Collections.Generic;
using UnityEngine;

namespace Game.AI
{
    [System.Serializable]
    public class SpawnPoint : MonoBehaviour
    {
        [SerializeField] private List<Vector3> waypoints = new();

        public Vector3 SpawnPosition => transform.position;
        public List<Vector3> Waypoints => waypoints;
        public int WaypointCount => waypoints.Count;

        private void OnDrawGizmos()
        {
            // Draw spawn point
            Gizmos.color = Color.green;
            Gizmos.DrawWireSphere(transform.position, 0.5f);

            // Draw waypoints and connections
            if (waypoints != null && waypoints.Count > 0)
            {
                Gizmos.color = Color.blue;
                var previousPoint = transform.position;

                for (var i = 0; i < waypoints.Count; i++)
                {
                    // Draw waypoint
                    Gizmos.DrawWireSphere(waypoints[i], 0.3f);

                    // Draw connection line
                    Gizmos.color = Color.yellow;
                    Gizmos.DrawLine(previousPoint, waypoints[i]);
                    previousPoint = waypoints[i];

                    Gizmos.color = Color.blue;
                }
            }
        }

        public void AddWaypoint(Vector3 worldPosition)
        {
            waypoints.Add(worldPosition);
        }

        public void ClearWaypoints()
        {
            waypoints.Clear();
        }

        public bool GetWaypoint(int index, out Vector3 value)
        {
            if (index >= 0 && index < waypoints.Count)
            {
                value = waypoints[index];

                return true;
            }

            value = Vector3.zero;

            return false;
        }
    }
}