using System.Linq;
using Core.Extensions;
using Game.Gameplay.Mechanics.Throwing;
using Game.Logic.Level;
using MessagePipe;
using Microsoft.Extensions.Logging;
using R3;
using Unity.Behavior;
using UnityEngine;
using UnityEngine.AI;
using VContainer;
using ZLogger;
using ILogger = Microsoft.Extensions.Logging.ILogger;

namespace Game.AI
{
    public class WandererBehavior : MonoBehaviour
    {
        [SerializeField] private NavMeshAgent navMeshAgent;
        [SerializeField] private BehaviorGraphAgent behavior;

        public Observable<float> SuspicionChanged => suspicionObservable;
        public int NpcId { get; private set; }

        private readonly float runningSpeed = 4f;
        private readonly ReactiveProperty<float> suspicionObservable = new();
        private readonly float walkingSpeed = 2f;
        private BlackboardVariable<float> actionDuration;
        private SpawnPoint assignedSpawnPoint;
        private int currentWaypointIndex;
        private LevelObjects levelObjects;
        private ILogger logger;
        private BlackboardVariable<Vector3> lookTarget;
        private Animator playerAnimator;
        private BlackboardVariable<float> suspicion;
        private BlackboardVariable<bool> throwableDetected;
        private ISubscriber<ThrowableExecutedSignal> throwableExecuted;

        [Inject] internal void Inject(ILoggerFactory loggerFactory,
                                      LevelObjects levelObjects,
                                      ISubscriber<ThrowableExecutedSignal> throwableExecuted)
        {
            this.throwableExecuted = throwableExecuted;
            this.levelObjects = levelObjects;
            logger = loggerFactory.CreateLogger(GetType());
        }

        private void Awake()
        {
            navMeshAgent = GetComponent<NavMeshAgent>();
        }

        private void Start()
        {
            var blackboardReference = behavior.BlackboardReference;
            blackboardReference.SetVariableValue("SpawnPosition", transform.position);
            blackboardReference.SetVariableValue("Player", levelObjects.Player?.gameObject);

            // Set spawn point in blackboard if assigned
            if (assignedSpawnPoint != null)
            {
                blackboardReference.SetVariableValue("SpawnPoint", assignedSpawnPoint);
            }

            blackboardReference.GetVariable("Suspicion", out suspicion);
            blackboardReference.GetVariable("ThrowableDetected", out throwableDetected);
            blackboardReference.GetVariable("ActionDuration", out actionDuration);
            blackboardReference.GetVariable("LookTarget", out lookTarget);

            // find a way to handle reactions in a better way

            GetComponentsInChildren<ISuspicionSensor>()
                .Select(x => x.ModifierStream)
                .Merge()

                // .SkipWhile(_ => reactionPending.Value)
                .Subscribe(OnSuspicionModified)
                .AddTo(this);

            throwableExecuted.Subscribe(OnThrowableExecuted);
        }

        private void Update()
        {
            if (playerAnimator != null)
            {
                playerAnimator.SetFloat("move_speed", navMeshAgent.velocity.magnitude * 0.9f / walkingSpeed / 2f);
            }

            if (navMeshAgent.isStopped)
            {
                return;
            }

            if (navMeshAgent.pathPending)
            {
                return;
            }

            if (navMeshAgent.remainingDistance <= navMeshAgent.stoppingDistance)
            {
                AdvanceToNextWaypoint();
            }

            if (!navMeshAgent.hasPath)
            {
                SetNavigationTarget();
            }
        }

        public bool Navigate()
        {
            // set next destination only when finished current

            if (!NavMeshActive())
            {
                return false;
            }

            navMeshAgent.isStopped = false;

            if (!navMeshAgent.hasPath)
            {
                SetNavigationTarget();
            }

            return true;
        }

        /// <summary>
        /// Pause waypoint navigation (for behavior interruptions)
        /// </summary>
        public void PauseWaypointNavigation()
        {
            if (NavMeshActive())
            {
                navMeshAgent.isStopped = true;
            }
        }

        public void SetRunning(bool state)
        {
            navMeshAgent.speed = state ? runningSpeed : walkingSpeed;
        }

        public void SetSkin(int id, GameObject skin)
        {
            NpcId = id;
            skin = Instantiate(skin, transform);
            playerAnimator = skin.GetComponent<Animator>();
        }

        public void SetSpawnPoint(SpawnPoint spawnPoint)
        {
            assignedSpawnPoint = spawnPoint;
            currentWaypointIndex = 0;
        }

        private void OnThrowableExecuted(ThrowableExecutedSignal obj)
        {
            if (Vector3.Distance(obj.WorldPosition, transform.position) <= obj.Throwable.ActivationDistance)
            {
                throwableDetected.Value = true;
                lookTarget.Value = obj.WorldPosition;
                actionDuration.Value = obj.Throwable.Duration;
            }
        }

        private void OnSuspicionModified(float x)
        {
            var newSuspicionValue = Mathf.Clamp(suspicion.Value + x, 0, 1);
            suspicion.Value = newSuspicionValue;
            suspicionObservable.OnNext(newSuspicionValue);
        }

        private bool NavMeshActive()
        {
            return navMeshAgent != null && navMeshAgent is { isActiveAndEnabled: true, isOnNavMesh: true };
        }

        private static bool IsNavigationComplete(NavMeshAgent agent)
        {
            return !agent.pathPending && agent.remainingDistance <= agent.stoppingDistance;
        }

        private bool SetNavigationTarget()
        {
            if (assignedSpawnPoint.Waypoints.TryGetByIndex(currentWaypointIndex, out var target))
            {
                navMeshAgent.SetDestination(target);

                return true;
            }

            return true;
        }

        /// <summary>
        /// Move to the next waypoint in the sequence
        /// </summary>
        private void AdvanceToNextWaypoint()
        {
            currentWaypointIndex++;

            // Check if we've reached the end of waypoints
            if (currentWaypointIndex >= assignedSpawnPoint.WaypointCount)
            {
                // Loop back to the first waypoint
                currentWaypointIndex = 0;
                logger.ZLogDebug($"{gameObject.name} completed waypoint loop, restarting from waypoint 0");
            }
        }
    }
}