using System.Collections.Generic;
using Core.Extensions;
using Game.AI;
using Game.Data;
using Game.Gameplay.Mechanics.Stealing;
using Game.Gameplay.ViewModels;
using Microsoft.Extensions.Logging;
using R3;
using R3.Triggers;
using UnityEngine;
using VContainer;
using ZLogger;
using ILogger = Microsoft.Extensions.Logging.ILogger;

namespace Game.Characters
{
    public class VictimTracker : MonoBehaviour
    {
        [Inject] internal GameConfigService gameConfigService;
        [Inject] internal GameLootController gameLootController;

        private readonly List<Transform> wanderersInRange = new();

        private Transform? lastVictim;

        private ILogger logger;

        [Inject]
        internal void Inject(ILoggerFactory loggerFactory)
        {
            logger = loggerFactory.CreateLogger(GetType());
        }

        private void Start()
        {
            var distanceSettings = gameConfigService.GameConfig.DistanceSettings;
            var capsuleCollider = GetComponent<CapsuleCollider>();

            if (capsuleCollider != null)
            {
                capsuleCollider.radius = distanceSettings.VictimTrackingRange;
            }

            this.OnTriggerEnterAsObservable().Where(WandererSelector).Subscribe(OnWandererEnterTrackingRange).AddTo(this);
            this.OnTriggerExitAsObservable().Where(WandererSelector).Subscribe(OnWandererExitTrackingRange).AddTo(this);
        }

        private void OnWandererEnterTrackingRange(Collider x)
        {
            wanderersInRange.Add(x.transform);

            if (lastVictim == null)
            {
                FindClosestVictim();
            }
        }

        private void OnWandererExitTrackingRange(Collider x)
        {
            wanderersInRange.Remove(x.transform);

            if (lastVictim == x.transform)
            {
                logger.ZLogDebug($"Last victim left tracking range");
                lastVictim = null;
                gameLootController.SelectVictim(null);
                FindClosestVictim();
            }
        }

        private bool FindClosestVictim()
        {
            wanderersInRange.Sort(new DistanceComparer(transform));

            if (!wanderersInRange.TryGetByIndex(0, out var closestWandererTransform))
            {
                logger.ZLogDebug($"No new victims in range");
                gameLootController.SelectVictim(null);

                return false;
            }

            lastVictim = closestWandererTransform;
            var wandererBehavior = closestWandererTransform.GetComponent<WandererBehavior>();

            var targetVictimInfo = new TargetVictimInfo
            {
                FollowTransform = GetFollowTransform(closestWandererTransform),
                NpcTransform = closestWandererTransform,
                NpcId = wandererBehavior.NpcId,
            };

            logger.ZLogDebug($"Selecting new victim {targetVictimInfo.NpcId.ToString():npc_id}");
            gameLootController.SelectVictim(targetVictimInfo);

            return true;
        }

        private Transform GetFollowTransform(Transform target)
        {
            return target.Find("UI");
        }

        private bool WandererSelector(Collider x)
        {
            return x.gameObject.layer == LayerMask.NameToLayer("Wanderer");
        }

        private class DistanceComparer : IComparer<Transform>
        {
            private readonly Transform self;

            public DistanceComparer(Transform self)
            {
                this.self = self;
            }

            public int Compare(Transform? x, Transform? y)
            {
                if (x != null && y != null)
                {
                    return (int)(Vector3.Distance(self.position, x.position) - Vector3.Distance(self.position, y.position) * 100);
                }

                return 0;
            }
        }
    }
}