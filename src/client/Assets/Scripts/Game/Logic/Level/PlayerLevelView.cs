using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using Game.Data;
using Microsoft.Extensions.Logging;
using ObservableCollections;
using R3;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UnityEngine.UIElements;
using VContainer;
using ZLogger;
using ILogger = Microsoft.Extensions.Logging.ILogger;

namespace Game.Logic.Level
{
    public class PlayerLevelView : MonoBehaviour
    {
        [Inject] internal GameConfigService gameConfigService;
        [Inject] internal PlayerLevelViewModel viewModel;
        private readonly List<InventorySlotData> inventorySlots = new();
        private ILogger logger;
        private IDisposable? storageSubscription;

        private ViewData? viewData;

        [Inject]
        internal void Inject(ILoggerFactory loggerFactory)
        {
            logger = loggerFactory.CreateLogger(GetType());
        }

        private void Awake()
        {
            var uiDocument = GetComponent<UIDocument>();

            viewData = new ViewData
            {
                TimeLeft = uiDocument.rootVisualElement.Query<Label>("TimeLeft"),
                InventoryContainer = uiDocument.rootVisualElement.Query<VisualElement>("InventoryContainer"),
            };

            // Initialize inventory slots
            for (var i = 0; i < 8; i++)
            {
                var slotElement = uiDocument.rootVisualElement.Q($"InventorySlot{i}");

                if (slotElement != null)
                {
                    inventorySlots.Add(new InventorySlotData
                    {
                        SlotElement = slotElement,
                        ItemImage = null,
                        CountLabel = null,
                        IsEmpty = true,
                    });

                    // Set initial empty state
                    slotElement.AddToClassList("hud-inventory-slot-empty");
                }
            }
        }

        private void Start()
        {
            if (viewData != null)
            {
                // Subscribe to time changes
                viewModel.TimeLeftSeconds.Subscribe(x =>
                {
                    var timeSpan = TimeSpan.FromSeconds(x);
                    viewData.TimeLeft.text = timeSpan.ToString(@"mm\:ss");
                }).AddTo(this);

                // Subscribe to inventory changes
                SubscribeToInventoryChanges();
            }
        }

        private void OnDestroy()
        {
            storageSubscription?.Dispose();
        }

        private void SubscribeToInventoryChanges()
        {
            var bag = new DisposableBag();

            // Subscribe to storage collection changes
            var view = viewModel.Storage.Storage.CreateView(item =>
            {
                logger.ZLogInformation($"New item added to inventory: {item.ItemId} x{item.Count.Value}");
                UpdateInventoryDisplay();

                return item;
            }).AddTo(ref bag);

            view.ObserveRemove().Subscribe(x =>
            {
                logger.ZLogInformation($"Item removed from inventory at index {x.Index}");
                UpdateInventoryDisplay();
            }).AddTo(ref bag);

            // Subscribe to individual item count changes
            foreach (var item in viewModel.Storage.Storage)
            {
                item.Count.Subscribe(_ => UpdateInventoryDisplay()).AddTo(ref bag);
            }

            storageSubscription = bag;
        }

        private void UpdateInventoryDisplay()
        {
            // Clear all slots first
            for (var i = 0; i < inventorySlots.Count; i++)
            {
                var slot = inventorySlots[i];
                slot.SlotElement.Clear();
                slot.SlotElement.RemoveFromClassList("hud-inventory-slot-filled");
                slot.SlotElement.AddToClassList("hud-inventory-slot-empty");
                slot.IsEmpty = true;
            }

            // Fill slots with current items
            var storageItems = viewModel.Storage.Storage;

            for (var i = 0; i < Math.Min(storageItems.Count, inventorySlots.Count); i++)
            {
                var item = storageItems[i];
                var slot = inventorySlots[i];

                UpdateInventorySlot(slot, item).Forget();
            }
        }

        private async UniTaskVoid UpdateInventorySlot(InventorySlotData slot, ReactiveItemCount item)
        {
            try
            {
                // Get item data from config
                if (!gameConfigService.GameConfig.GetItemById(item.ItemId, out var itemData))
                {
                    logger.ZLogError($"Unable to get config for item {item.ItemId}");

                    return;
                }

                // Load icon from addressables
                var iconTexture = await Addressables.LoadAssetAsync<Texture2D>(itemData.LootIcon);

                // Create item container
                var itemContainer = new VisualElement();
                itemContainer.AddToClassList("hud-inventory-item");

                // Create icon container
                var iconContainer = new VisualElement();
                iconContainer.AddToClassList("hud-inventory-item-icon");

                // Create image element
                var imageElement = new VisualElement();
                imageElement.style.backgroundImage = new StyleBackground(iconTexture);
                imageElement.style.width = 40;
                imageElement.style.height = 40;
                imageElement.style.borderTopLeftRadius = 20;

                iconContainer.Add(imageElement);
                itemContainer.Add(iconContainer);

                // Create count label
                var countLabel = new Label(item.Count.Value.ToString());
                countLabel.AddToClassList("hud-inventory-item-count");
                itemContainer.Add(countLabel);

                // Update slot
                slot.SlotElement.Clear();
                slot.SlotElement.Add(itemContainer);
                slot.SlotElement.RemoveFromClassList("hud-inventory-slot-empty");
                slot.SlotElement.AddToClassList("hud-inventory-slot-filled");
                slot.IsEmpty = false;
                slot.ItemImage = imageElement;
                slot.CountLabel = countLabel;

                // Subscribe to count changes for this specific item
                item.Count.Subscribe(count =>
                {
                    if (slot.CountLabel != null)
                    {
                        slot.CountLabel.text = count.ToString();
                    }
                }).AddTo(this);
            }
            catch (Exception ex)
            {
                logger.ZLogError(ex, $"Failed to update inventory slot for item {item.ItemId}");
            }
        }

        private class InventorySlotData
        {
            public Label CountLabel;
            public bool IsEmpty;
            public VisualElement ItemImage;
            public VisualElement SlotElement;
        }

        private class ViewData
        {
            public VisualElement InventoryContainer;
            public Label TimeLeft;
        }
    }
}