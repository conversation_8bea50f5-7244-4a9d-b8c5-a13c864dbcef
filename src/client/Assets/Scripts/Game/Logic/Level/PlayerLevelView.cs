using System;
using R3;
using UnityEngine;
using UnityEngine.UIElements;
using VContainer;

namespace Game.Logic.Level
{
    public class PlayerLevelView : MonoBehaviour
    {
        [Inject] internal PlayerLevelViewModel viewModel;

        private ViewData? viewData;

        private void Awake()
        {
            var uiDocument = GetComponent<UIDocument>();

            viewData = new ViewData
            {
                TimeLeft = uiDocument.rootVisualElement.Query<Label>("TimeLeft"),

                // CashCollected = uiDocument.rootVisualElement.Query<Label>("CashCollected"),
            };
        }

        private void Start()
        {
            if (viewData != null)
            {
                // viewModel.TimeLeftSeconds.Subscribe(x =>
                // {
                //     var timeSpan = TimeSpan.FromSeconds(x);
                //     viewData.TimeLeft.text = timeSpan.ToString(@"mm\:ss");
                // }).AddTo(this);
            }
        }

        private class ViewData
        {
            public Label CashCollected;
            public Label TimeLeft;
        }
    }
}