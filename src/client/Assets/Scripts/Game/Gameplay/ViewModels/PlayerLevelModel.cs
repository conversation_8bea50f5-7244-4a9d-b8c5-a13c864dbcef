using System;
using Game.Gameplay.Mechanics.Stealing;
using MessagePipe;
using R3;
using DisposableBag = R3.DisposableBag;

namespace Game.Logic.Level
{
    public class PlayerLevelViewModel : IDisposable
    {
        private const int ROUND_TIME = 180;
        public ReactiveProperty<int> TimeLeftSeconds { get; } = new();
        public ObservableItemStorage Storage { get; } = new();

        private readonly ISubscriber<LevelStartedSignal> levelStarted;
        private readonly ISubscriber<StealingSuccessSignal> stealingSuccess;
        private DisposableBag disposable;

        public PlayerLevelViewModel(ISubscriber<LevelStartedSignal> levelStarted,
                                    ISubscriber<StealingSuccessSignal> stealingSuccess)
        {
            this.levelStarted = levelStarted;
            this.stealingSuccess = stealingSuccess;
            this.levelStarted.Subscribe(OnLevelStarted).AddTo(ref disposable);
            this.stealingSuccess.Subscribe(x => Storage.Add(x.GameItem)).AddTo(ref disposable);
        }

        public void Dispose()
        {
            disposable.Dispose();
        }

        private void OnLevelStarted(LevelStartedSignal obj)
        {
            Observable.Interval(TimeSpan.FromSeconds(1), UnityTimeProvider.EarlyUpdate)
                      .Index()
                      .Select((index, _) => ROUND_TIME - index)
                      .Subscribe(x => TimeLeftSeconds.OnNext(x));
        }
    }

}