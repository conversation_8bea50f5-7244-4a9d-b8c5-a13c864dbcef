using Game.Data;
using ObservableCollections;

namespace Game.Logic.Level
{
    public class ObservableItemStorage
    {
        public IReadOnlyObservableList<ReactiveItemCount> Storage => items;

        private readonly ObservableList<ReactiveItemCount> items = new();

        public bool Add(GameItem gameItem)
        {
            foreach (var reactiveItemCount in Storage)
            {
                if (reactiveItemCount.ItemId == gameItem.ItemId)
                {
                    reactiveItemCount.Count.OnNext(reactiveItemCount.Count.Value + gameItem.Count);

                    return true;
                }
            }

            items.Add(new ReactiveItemCount(gameItem));

            return true;
        }
    }
}