using System;
using Game.Gameplay.Mechanics.Stealing;
using Game.Gameplay.ViewData;
using ObservableCollections;
using UnityEngine;

namespace Game.Gameplay.ViewModels
{
    public class TargetVictimViewModel : IDisposable
    {
        public readonly ObservableList<LootStorageEntry> Loot = new();

        public readonly Action<int> Steal;

        public readonly TargetVictimInfo VictimInfo;
        private readonly GameItemCollection loot;

        public TargetVictimViewModel(TargetVictimInfo victimInfo, Action<int> steal, GameItemCollection loot)
        {
            VictimInfo = victimInfo;
            Steal = steal;
            this.loot = loot;
        }

        public void Dispose()
        {
            foreach (var lootStorageEntry in Loot)
            {
                lootStorageEntry.StealingProgress.Dispose();
            }
        }
    }

    public class TargetVictimInfo
    {
        public Transform FollowTransform;
        public int NpcId;
        public Transform NpcTransform;
    }

}