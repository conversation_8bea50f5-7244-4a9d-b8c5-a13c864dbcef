using Game.Data;
using ObservableCollections;

namespace Game.Gameplay.ViewData
{
    public class LootStorage
    {
        public readonly ObservableDictionary<uint, ReactiveLootStorageEntry> Loot = new();

        public void Add(GameItem item)
        {
            if (!Loot.TryGetValue(item.ItemId, out var existing))
            {
                Loot.Add(item.ItemId, new ReactiveLootStorageEntry(item.ItemId, item.Count));

                return;
            }

            existing.Count.Value += item.Count;
        }
    }

}