using DG.Tweening;
using Game.Data;
using Game.Gameplay.Mechanics.Throwing;
using MessagePipe;
using Microsoft.Extensions.Logging;
using UnityEngine;
using VContainer;
using ZLogger;
using ILogger = Microsoft.Extensions.Logging.ILogger;

namespace Game.Gameplay.Views
{
    /// <summary>
    /// Component that handles collision detection for thrown projectiles
    /// Stops the projectile animation when it hits something
    /// </summary>
    public class ProjectileCollisionDetector : MonoBehaviour
    {
        private ILogger logger;
        private Sequence sequence;
        private Throwable throwable;
        private IPublisher<ThrowableExecutedSignal> throwableExecuted;

        [Inject] internal void Inject(ILoggerFactory loggerFactory, IPublisher<ThrowableExecutedSignal> throwableExecuted)
        {
            this.throwableExecuted = throwableExecuted;
            logger = loggerFactory.CreateLogger(GetType());
        }

        public void Throw(Throwable throwable, Vector3[] pathPoints, float throwDuration)
        {
            this.throwable = throwable;
            sequence = DOTween.Sequence();
            sequence.Append(gameObject.transform.DOPath(pathPoints, throwDuration, PathType.CatmullRom).SetEase(Ease.Linear));
            sequence.OnComplete(OnThrowableOnGround);
        }

        private void OnThrowableOnGround()
        {
            logger.ZLogInformation($"Throwable fell on ground {gameObject.name:throwable_name}");

            throwableExecuted.Publish(new ThrowableExecutedSignal
            {
                Throwable = throwable,
                WorldPosition = transform.position,
            });
        }
    }

}