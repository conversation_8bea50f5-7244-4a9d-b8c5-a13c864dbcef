using Microsoft.Extensions.Logging;
using VContainer;
using VContainer.Unity;
using ZLogger;
using ZLogger.Unity;

namespace Game.DI.Bundles
{
    public class LoggingBundle : IInstaller
    {
        private static ILoggerFactory CreateLoggerFactory()
        {
            return LoggerFactory.Create(builder =>
            {
                builder.ClearProviders();
                builder.SetMinimumLevel(LogLevel.Debug);

                // builder.AddSentry();
                // builder.Services.AddSingleton<ILoggerProvider, SentryErrorProvider>();
                builder.AddZLoggerUnityDebug(options =>
                {
                    options.UsePlainTextFormatter(x =>
                    {
                        x.SetPrefixFormatter(
                            $"{0}:: ",
                            (in MessageTemplate template, in LogInfo info) => template.Format(info.Category.Name));
                    });
                });
            });
        }

        public void Install(IContainerBuilder builder)
        {
            var loggerFactory = CreateLoggerFactory();
            var containerLogger = loggerFactory.CreateLogger("VContainer");

            builder.RegisterEntryPointExceptionHandler(e =>
                containerLogger.ZLogCritical(e, $"Container level exception")
            );

            builder.RegisterInstance(loggerFactory);
        }
    }

}