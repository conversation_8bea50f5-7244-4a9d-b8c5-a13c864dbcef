using Game.Data;
using UnityEngine;
using VContainer;
using VContainer.Unity;

namespace Game.DI
{
    public class MainScope : LifetimeScope
    {
        [SerializeField] private StealingSettings stealingSettings;

        protected override void Configure(IContainerBuilder builder)
        {
            base.Configure(builder);
            builder.Register<GameConfigService>(Lifetime.Singleton);
            builder.RegisterInstance(stealingSettings);
        }
    }
}