using System.Collections.Generic;
using System.Linq;

namespace Game.Data
{
    public class GameConfig
    {

        public DistanceSettings DistanceSettings = new()
        {
            VictimTrackingRange = 3,
            WandererSuspicionRange = 1,
            OptimalStealingDistance = 2,
            BumpDistance = 0.3f,
        };
        public List<ItemData> Items = new()
        {
            new ItemData
            {
                ItemId = 1,
                Name = "Money",
                LootIcon = "loot_money",
                BaseTimeToSteal = 5,
            },
            new ItemData
            {
                ItemId = 2,
                Name = "Headphones",
                LootIcon = "loot_headphones",
                BaseTimeToSteal = 3,
            },
            new ItemData
            {
                ItemId = 3,
                Name = "Ring",
                LootIcon = "loot_ring",
                BaseTimeToSteal = 10,
            },
            new ItemData
            {
                ItemId = 4,
                Name = "Letter",
                LootIcon = "loot_letter",
                BaseTimeToSteal = 5,
            },
            new ItemData
            {
                ItemId = 5,
                Name = "Car keys",
                LootIcon = "loot_car_keys",
                BaseTimeToSteal = 5,
            },
            new ItemData
            {
                ItemId = 6,
                Name = "Umbrella",
                LootIcon = "loot_umbrella",
                BaseTimeToSteal = 8,
            },
            new ItemData
            {
                ItemId = 7,
                Name = "Pen",
                LootIcon = "loot_pen",
                BaseTimeToSteal = 7,
            },
            new ItemData
            {
                ItemId = 8,
                Name = "Keys",
                LootIcon = "loot_keys",
                BaseTimeToSteal = 6,
            },
        };

        public bool GetItemById(uint id, out ItemData data)
        {
            data = Items.FirstOrDefault(x => x.ItemId == id);

            return data != null;
        }
    }

}