namespace Game.Data
{
    public readonly struct GameItem
    {
        /// <summary>
        /// Unique identification of an item.
        /// </summary>
        public readonly uint ItemId;

        /// <summary>
        /// Count of this item
        /// </summary>
        public readonly ulong Count;

        public GameItem(uint itemId, ulong count)
        {
            ItemId = itemId;
            Count = count;
        }

        public static GameItem Bucks(ulong count)
        {
            return new GameItem(1, count);
        }
    }
}