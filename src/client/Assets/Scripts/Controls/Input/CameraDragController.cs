using R3;
using UnityEngine;
using UnityEngine.UIElements;

namespace Game.UI.Input
{
    public class CameraDragController : MonoBehaviour, ICameraDragInput
    {
        public Observable<Vector2> Input => reactiveProperty;

        private readonly ReactiveProperty<Vector2> reactiveProperty = new();

        private JoystickTouch? joystickTouch;

        private void OnEnable()
        {
            var uiDocument = GetComponent<UIDocument>();
            var controlsRoot = uiDocument.rootVisualElement.Q("Controls");
            var cameraRotator = controlsRoot.Q("camera-rotator");

            cameraRotator.RegisterCallback<PointerDownEvent>(OnPointerDown);
            controlsRoot.RegisterCallback<PointerMoveEvent>(OnPointerMove);
            controlsRoot.RegisterCallback<PointerUpEvent>(OnPointerUp);
        }

        private void OnPointerUp(PointerUpEvent evt)
        {
            if (joystickTouch != null && joystickTouch.PointerId == evt.pointerId)
            {
                joystickTouch = null;
            }
        }

        private void OnPointerMove(PointerMoveEvent evt)
        {
            if (joystickTouch != null && joystickTouch.PointerId == evt.pointerId)
            {
                reactiveProperty.OnNext(evt.deltaPosition);
            }
        }

        private void OnPointerDown(PointerDownEvent evt)
        {
            joystickTouch = new JoystickTouch
            {
                IsFirstPointerDown = true,
                PointerId = evt.pointerId,
                ScreenPosition = evt.position,
            };
        }
    }

}