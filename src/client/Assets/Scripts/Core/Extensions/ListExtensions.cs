using System.Collections.Generic;

namespace Core.Extensions
{
    public static class ListExtensions
    {
        public static bool TryGetByIndex<T>(this IList<T>? list, int index, out T value)
        {
            if (list == null)
            {
                value = default;

                return false;
            }

            if (index < 0)
            {
                value = default;

                return false;
            }

            if (list.Count <= index)
            {
                value = default;

                return false;
            }

            value = list[index];

            return true;
        }
    }
}