<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>Microsoft.Extensions.DependencyModel</name>
  </assembly>
  <members>
    <member name="T:Microsoft.DotNet.PlatformAbstractions.HashCodeCombiner" />
    <member name="M:Microsoft.DotNet.PlatformAbstractions.HashCodeCombiner.Add(System.Int32)">
      <param name="i" />
    </member>
    <member name="M:Microsoft.DotNet.PlatformAbstractions.HashCodeCombiner.Add(System.Object)">
      <param name="o" />
    </member>
    <member name="M:Microsoft.DotNet.PlatformAbstractions.HashCodeCombiner.Add(System.String)">
      <param name="s" />
    </member>
    <member name="M:Microsoft.DotNet.PlatformAbstractions.HashCodeCombiner.Add``1(``0,System.Collections.Generic.IEqualityComparer{``0})">
      <param name="value" />
      <param name="comparer" />
      <typeparam name="TValue" />
    </member>
    <member name="M:Microsoft.DotNet.PlatformAbstractions.HashCodeCombiner.Start" />
    <member name="P:Microsoft.DotNet.PlatformAbstractions.HashCodeCombiner.CombinedHash" />
    <member name="T:Microsoft.Extensions.DependencyModel.CompilationLibrary" />
    <member name="M:Microsoft.Extensions.DependencyModel.CompilationLibrary.#ctor(System.String,System.String,System.String,System.String,System.Collections.Generic.IEnumerable{System.String},System.Collections.Generic.IEnumerable{Microsoft.Extensions.DependencyModel.Dependency},System.Boolean)">
      <param name="type" />
      <param name="name" />
      <param name="version" />
      <param name="hash" />
      <param name="assemblies" />
      <param name="dependencies" />
      <param name="serviceable" />
    </member>
    <member name="M:Microsoft.Extensions.DependencyModel.CompilationLibrary.#ctor(System.String,System.String,System.String,System.String,System.Collections.Generic.IEnumerable{System.String},System.Collections.Generic.IEnumerable{Microsoft.Extensions.DependencyModel.Dependency},System.Boolean,System.String,System.String)">
      <param name="type" />
      <param name="name" />
      <param name="version" />
      <param name="hash" />
      <param name="assemblies" />
      <param name="dependencies" />
      <param name="serviceable" />
      <param name="path" />
      <param name="hashPath" />
    </member>
    <member name="M:Microsoft.Extensions.DependencyModel.CompilationLibrary.ResolveReferencePaths" />
    <member name="M:Microsoft.Extensions.DependencyModel.CompilationLibrary.ResolveReferencePaths(Microsoft.Extensions.DependencyModel.Resolution.ICompilationAssemblyResolver[])">
      <param name="customResolvers" />
    </member>
    <member name="P:Microsoft.Extensions.DependencyModel.CompilationLibrary.Assemblies" />
    <member name="T:Microsoft.Extensions.DependencyModel.CompilationOptions" />
    <member name="M:Microsoft.Extensions.DependencyModel.CompilationOptions.#ctor(System.Collections.Generic.IEnumerable{System.String},System.String,System.String,System.Nullable{System.Boolean},System.Nullable{System.Boolean},System.Nullable{System.Boolean},System.String,System.Nullable{System.Boolean},System.Nullable{System.Boolean},System.String,System.Nullable{System.Boolean},System.Nullable{System.Boolean})">
      <param name="defines" />
      <param name="languageVersion" />
      <param name="platform" />
      <param name="allowUnsafe" />
      <param name="warningsAsErrors" />
      <param name="optimize" />
      <param name="keyFile" />
      <param name="delaySign" />
      <param name="publicSign" />
      <param name="debugType" />
      <param name="emitEntryPoint" />
      <param name="generateXmlDocumentation" />
    </member>
    <member name="P:Microsoft.Extensions.DependencyModel.CompilationOptions.AllowUnsafe" />
    <member name="P:Microsoft.Extensions.DependencyModel.CompilationOptions.DebugType" />
    <member name="P:Microsoft.Extensions.DependencyModel.CompilationOptions.Default" />
    <member name="P:Microsoft.Extensions.DependencyModel.CompilationOptions.Defines" />
    <member name="P:Microsoft.Extensions.DependencyModel.CompilationOptions.DelaySign" />
    <member name="P:Microsoft.Extensions.DependencyModel.CompilationOptions.EmitEntryPoint" />
    <member name="P:Microsoft.Extensions.DependencyModel.CompilationOptions.GenerateXmlDocumentation" />
    <member name="P:Microsoft.Extensions.DependencyModel.CompilationOptions.KeyFile" />
    <member name="P:Microsoft.Extensions.DependencyModel.CompilationOptions.LanguageVersion" />
    <member name="P:Microsoft.Extensions.DependencyModel.CompilationOptions.Optimize" />
    <member name="P:Microsoft.Extensions.DependencyModel.CompilationOptions.Platform" />
    <member name="P:Microsoft.Extensions.DependencyModel.CompilationOptions.PublicSign" />
    <member name="P:Microsoft.Extensions.DependencyModel.CompilationOptions.WarningsAsErrors" />
    <member name="T:Microsoft.Extensions.DependencyModel.Dependency" />
    <member name="M:Microsoft.Extensions.DependencyModel.Dependency.#ctor(System.String,System.String)">
      <param name="name" />
      <param name="version" />
    </member>
    <member name="M:Microsoft.Extensions.DependencyModel.Dependency.Equals(Microsoft.Extensions.DependencyModel.Dependency)">
      <summary>Indicates whether the current object is equal to another object of the same type.</summary>
      <param name="other">An object to compare with this object.</param>
      <returns>
        <see langword="true" /> if the current object is equal to the <paramref name="other" /> parameter; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyModel.Dependency.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:Microsoft.Extensions.DependencyModel.Dependency.GetHashCode" />
    <member name="P:Microsoft.Extensions.DependencyModel.Dependency.Name" />
    <member name="P:Microsoft.Extensions.DependencyModel.Dependency.Version" />
    <member name="T:Microsoft.Extensions.DependencyModel.DependencyContext">
      <summary>Provides information about application dependencies.</summary>
    </member>
    <member name="M:Microsoft.Extensions.DependencyModel.DependencyContext.#ctor(Microsoft.Extensions.DependencyModel.TargetInfo,Microsoft.Extensions.DependencyModel.CompilationOptions,System.Collections.Generic.IEnumerable{Microsoft.Extensions.DependencyModel.CompilationLibrary},System.Collections.Generic.IEnumerable{Microsoft.Extensions.DependencyModel.RuntimeLibrary},System.Collections.Generic.IEnumerable{Microsoft.Extensions.DependencyModel.RuntimeFallbacks})">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Extensions.DependencyModel.DependencyContext" /> class.</summary>
      <param name="target">Target runtime information.</param>
      <param name="compilationOptions">Compilation options.</param>
      <param name="compileLibraries">Compilation libraries.</param>
      <param name="runtimeLibraries">Runtime libraries.</param>
      <param name="runtimeGraph">Runtime identifiers graph.</param>
    </member>
    <member name="M:Microsoft.Extensions.DependencyModel.DependencyContext.Load(System.Reflection.Assembly)">
      <summary>Loads the dependency context for the specified assembly.</summary>
      <param name="assembly">The assembly to load dependency context for.</param>
      <returns>The dependency context for the specified assembly, or <c>null</c> when dependency context is not available.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyModel.DependencyContext.Merge(Microsoft.Extensions.DependencyModel.DependencyContext)">
      <summary>Merges the current dependency context with the specifed one.</summary>
      <param name="other">The dependency context to merge.</param>
      <returns>The dependency context that results from merging dependencies of the current context with the specified one.</returns>
    </member>
    <member name="P:Microsoft.Extensions.DependencyModel.DependencyContext.CompilationOptions">
      <summary>Gets the compilation options used to compile the application.</summary>
      <returns>The compilation options used to compile the application.</returns>
    </member>
    <member name="P:Microsoft.Extensions.DependencyModel.DependencyContext.CompileLibraries">
      <summary>Gets the list of libraries used to compile the application.</summary>
      <returns>The list of libraries used to compile the application.</returns>
    </member>
    <member name="P:Microsoft.Extensions.DependencyModel.DependencyContext.Default">
      <summary>Gets the dependency context for the current application.</summary>
      <returns>The dependency context for the current application.</returns>
    </member>
    <member name="P:Microsoft.Extensions.DependencyModel.DependencyContext.RuntimeGraph">
      <summary>Gets a runtime identifiers graph.</summary>
      <returns>A runtime identifiers graph.</returns>
    </member>
    <member name="P:Microsoft.Extensions.DependencyModel.DependencyContext.RuntimeLibraries">
      <summary>Gets the list of libraries used by the application at run time.</summary>
      <returns>The list of libraries used by the application at run time.</returns>
    </member>
    <member name="P:Microsoft.Extensions.DependencyModel.DependencyContext.Target">
      <summary>Gets information about the application's target runtime.</summary>
      <returns>The application's target runtime information.</returns>
    </member>
    <member name="T:Microsoft.Extensions.DependencyModel.DependencyContextExtensions" />
    <member name="M:Microsoft.Extensions.DependencyModel.DependencyContextExtensions.GetDefaultAssemblyNames(Microsoft.Extensions.DependencyModel.DependencyContext)">
      <param name="self" />
    </member>
    <member name="M:Microsoft.Extensions.DependencyModel.DependencyContextExtensions.GetDefaultAssemblyNames(Microsoft.Extensions.DependencyModel.RuntimeLibrary,Microsoft.Extensions.DependencyModel.DependencyContext)">
      <param name="self" />
      <param name="context" />
    </member>
    <member name="M:Microsoft.Extensions.DependencyModel.DependencyContextExtensions.GetDefaultNativeAssets(Microsoft.Extensions.DependencyModel.DependencyContext)">
      <param name="self" />
    </member>
    <member name="M:Microsoft.Extensions.DependencyModel.DependencyContextExtensions.GetDefaultNativeAssets(Microsoft.Extensions.DependencyModel.RuntimeLibrary,Microsoft.Extensions.DependencyModel.DependencyContext)">
      <param name="self" />
      <param name="context" />
    </member>
    <member name="M:Microsoft.Extensions.DependencyModel.DependencyContextExtensions.GetDefaultNativeRuntimeFileAssets(Microsoft.Extensions.DependencyModel.DependencyContext)">
      <param name="self" />
    </member>
    <member name="M:Microsoft.Extensions.DependencyModel.DependencyContextExtensions.GetDefaultNativeRuntimeFileAssets(Microsoft.Extensions.DependencyModel.RuntimeLibrary,Microsoft.Extensions.DependencyModel.DependencyContext)">
      <param name="self" />
      <param name="context" />
    </member>
    <member name="M:Microsoft.Extensions.DependencyModel.DependencyContextExtensions.GetRuntimeAssemblyNames(Microsoft.Extensions.DependencyModel.DependencyContext,System.String)">
      <param name="self" />
      <param name="runtimeIdentifier" />
    </member>
    <member name="M:Microsoft.Extensions.DependencyModel.DependencyContextExtensions.GetRuntimeAssemblyNames(Microsoft.Extensions.DependencyModel.RuntimeLibrary,Microsoft.Extensions.DependencyModel.DependencyContext,System.String)">
      <param name="self" />
      <param name="context" />
      <param name="runtimeIdentifier" />
    </member>
    <member name="M:Microsoft.Extensions.DependencyModel.DependencyContextExtensions.GetRuntimeNativeAssets(Microsoft.Extensions.DependencyModel.DependencyContext,System.String)">
      <param name="self" />
      <param name="runtimeIdentifier" />
    </member>
    <member name="M:Microsoft.Extensions.DependencyModel.DependencyContextExtensions.GetRuntimeNativeAssets(Microsoft.Extensions.DependencyModel.RuntimeLibrary,Microsoft.Extensions.DependencyModel.DependencyContext,System.String)">
      <param name="self" />
      <param name="context" />
      <param name="runtimeIdentifier" />
    </member>
    <member name="M:Microsoft.Extensions.DependencyModel.DependencyContextExtensions.GetRuntimeNativeRuntimeFileAssets(Microsoft.Extensions.DependencyModel.DependencyContext,System.String)">
      <param name="self" />
      <param name="runtimeIdentifier" />
    </member>
    <member name="M:Microsoft.Extensions.DependencyModel.DependencyContextExtensions.GetRuntimeNativeRuntimeFileAssets(Microsoft.Extensions.DependencyModel.RuntimeLibrary,Microsoft.Extensions.DependencyModel.DependencyContext,System.String)">
      <param name="self" />
      <param name="context" />
      <param name="runtimeIdentifier" />
    </member>
    <member name="T:Microsoft.Extensions.DependencyModel.DependencyContextJsonReader" />
    <member name="M:Microsoft.Extensions.DependencyModel.DependencyContextJsonReader.#ctor" />
    <member name="M:Microsoft.Extensions.DependencyModel.DependencyContextJsonReader.Dispose">
      <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary>
    </member>
    <member name="M:Microsoft.Extensions.DependencyModel.DependencyContextJsonReader.Dispose(System.Boolean)">
      <param name="disposing" />
    </member>
    <member name="M:Microsoft.Extensions.DependencyModel.DependencyContextJsonReader.Read(System.IO.Stream)">
      <param name="stream" />
    </member>
    <member name="T:Microsoft.Extensions.DependencyModel.DependencyContextLoader" />
    <member name="M:Microsoft.Extensions.DependencyModel.DependencyContextLoader.#ctor" />
    <member name="M:Microsoft.Extensions.DependencyModel.DependencyContextLoader.Load(System.Reflection.Assembly)">
      <param name="assembly" />
    </member>
    <member name="P:Microsoft.Extensions.DependencyModel.DependencyContextLoader.Default" />
    <member name="T:Microsoft.Extensions.DependencyModel.DependencyContextWriter" />
    <member name="M:Microsoft.Extensions.DependencyModel.DependencyContextWriter.#ctor" />
    <member name="M:Microsoft.Extensions.DependencyModel.DependencyContextWriter.Write(Microsoft.Extensions.DependencyModel.DependencyContext,System.IO.Stream)">
      <param name="context" />
      <param name="stream" />
    </member>
    <member name="T:Microsoft.Extensions.DependencyModel.IDependencyContextReader" />
    <member name="M:Microsoft.Extensions.DependencyModel.IDependencyContextReader.Read(System.IO.Stream)">
      <param name="stream" />
    </member>
    <member name="T:Microsoft.Extensions.DependencyModel.Library" />
    <member name="M:Microsoft.Extensions.DependencyModel.Library.#ctor(System.String,System.String,System.String,System.String,System.Collections.Generic.IEnumerable{Microsoft.Extensions.DependencyModel.Dependency},System.Boolean)">
      <param name="type" />
      <param name="name" />
      <param name="version" />
      <param name="hash" />
      <param name="dependencies" />
      <param name="serviceable" />
    </member>
    <member name="M:Microsoft.Extensions.DependencyModel.Library.#ctor(System.String,System.String,System.String,System.String,System.Collections.Generic.IEnumerable{Microsoft.Extensions.DependencyModel.Dependency},System.Boolean,System.String,System.String)">
      <param name="type" />
      <param name="name" />
      <param name="version" />
      <param name="hash" />
      <param name="dependencies" />
      <param name="serviceable" />
      <param name="path" />
      <param name="hashPath" />
    </member>
    <member name="M:Microsoft.Extensions.DependencyModel.Library.#ctor(System.String,System.String,System.String,System.String,System.Collections.Generic.IEnumerable{Microsoft.Extensions.DependencyModel.Dependency},System.Boolean,System.String,System.String,System.String)">
      <param name="type" />
      <param name="name" />
      <param name="version" />
      <param name="hash" />
      <param name="dependencies" />
      <param name="serviceable" />
      <param name="path" />
      <param name="hashPath" />
      <param name="runtimeStoreManifestName" />
    </member>
    <member name="P:Microsoft.Extensions.DependencyModel.Library.Dependencies" />
    <member name="P:Microsoft.Extensions.DependencyModel.Library.Hash" />
    <member name="P:Microsoft.Extensions.DependencyModel.Library.HashPath" />
    <member name="P:Microsoft.Extensions.DependencyModel.Library.Name" />
    <member name="P:Microsoft.Extensions.DependencyModel.Library.Path" />
    <member name="P:Microsoft.Extensions.DependencyModel.Library.RuntimeStoreManifestName" />
    <member name="P:Microsoft.Extensions.DependencyModel.Library.Serviceable" />
    <member name="P:Microsoft.Extensions.DependencyModel.Library.Type" />
    <member name="P:Microsoft.Extensions.DependencyModel.Library.Version" />
    <member name="T:Microsoft.Extensions.DependencyModel.Resolution.AppBaseCompilationAssemblyResolver" />
    <member name="M:Microsoft.Extensions.DependencyModel.Resolution.AppBaseCompilationAssemblyResolver.#ctor" />
    <member name="M:Microsoft.Extensions.DependencyModel.Resolution.AppBaseCompilationAssemblyResolver.#ctor(System.String)">
      <param name="basePath" />
    </member>
    <member name="M:Microsoft.Extensions.DependencyModel.Resolution.AppBaseCompilationAssemblyResolver.TryResolveAssemblyPaths(Microsoft.Extensions.DependencyModel.CompilationLibrary,System.Collections.Generic.List{System.String})">
      <param name="library" />
      <param name="assemblies" />
    </member>
    <member name="T:Microsoft.Extensions.DependencyModel.Resolution.CompositeCompilationAssemblyResolver" />
    <member name="M:Microsoft.Extensions.DependencyModel.Resolution.CompositeCompilationAssemblyResolver.#ctor(Microsoft.Extensions.DependencyModel.Resolution.ICompilationAssemblyResolver[])">
      <param name="resolvers" />
    </member>
    <member name="M:Microsoft.Extensions.DependencyModel.Resolution.CompositeCompilationAssemblyResolver.TryResolveAssemblyPaths(Microsoft.Extensions.DependencyModel.CompilationLibrary,System.Collections.Generic.List{System.String})">
      <param name="library" />
      <param name="assemblies" />
    </member>
    <member name="T:Microsoft.Extensions.DependencyModel.Resolution.DotNetReferenceAssembliesPathResolver" />
    <member name="F:Microsoft.Extensions.DependencyModel.Resolution.DotNetReferenceAssembliesPathResolver.DotNetReferenceAssembliesPathEnv" />
    <member name="M:Microsoft.Extensions.DependencyModel.Resolution.DotNetReferenceAssembliesPathResolver.#ctor" />
    <member name="M:Microsoft.Extensions.DependencyModel.Resolution.DotNetReferenceAssembliesPathResolver.Resolve" />
    <member name="T:Microsoft.Extensions.DependencyModel.Resolution.ICompilationAssemblyResolver" />
    <member name="M:Microsoft.Extensions.DependencyModel.Resolution.ICompilationAssemblyResolver.TryResolveAssemblyPaths(Microsoft.Extensions.DependencyModel.CompilationLibrary,System.Collections.Generic.List{System.String})">
      <param name="library" />
      <param name="assemblies" />
    </member>
    <member name="T:Microsoft.Extensions.DependencyModel.Resolution.PackageCompilationAssemblyResolver" />
    <member name="M:Microsoft.Extensions.DependencyModel.Resolution.PackageCompilationAssemblyResolver.#ctor" />
    <member name="M:Microsoft.Extensions.DependencyModel.Resolution.PackageCompilationAssemblyResolver.#ctor(System.String)">
      <param name="nugetPackageDirectory" />
    </member>
    <member name="M:Microsoft.Extensions.DependencyModel.Resolution.PackageCompilationAssemblyResolver.TryResolveAssemblyPaths(Microsoft.Extensions.DependencyModel.CompilationLibrary,System.Collections.Generic.List{System.String})">
      <param name="library" />
      <param name="assemblies" />
    </member>
    <member name="T:Microsoft.Extensions.DependencyModel.Resolution.ReferenceAssemblyPathResolver" />
    <member name="M:Microsoft.Extensions.DependencyModel.Resolution.ReferenceAssemblyPathResolver.#ctor" />
    <member name="M:Microsoft.Extensions.DependencyModel.Resolution.ReferenceAssemblyPathResolver.#ctor(System.String,System.String[])">
      <param name="defaultReferenceAssembliesPath" />
      <param name="fallbackSearchPaths" />
    </member>
    <member name="M:Microsoft.Extensions.DependencyModel.Resolution.ReferenceAssemblyPathResolver.TryResolveAssemblyPaths(Microsoft.Extensions.DependencyModel.CompilationLibrary,System.Collections.Generic.List{System.String})">
      <param name="library" />
      <param name="assemblies" />
    </member>
    <member name="T:Microsoft.Extensions.DependencyModel.ResourceAssembly" />
    <member name="M:Microsoft.Extensions.DependencyModel.ResourceAssembly.#ctor(System.String,System.String)">
      <param name="path" />
      <param name="locale" />
    </member>
    <member name="P:Microsoft.Extensions.DependencyModel.ResourceAssembly.Locale" />
    <member name="P:Microsoft.Extensions.DependencyModel.ResourceAssembly.Path" />
    <member name="T:Microsoft.Extensions.DependencyModel.RuntimeAssembly" />
    <member name="M:Microsoft.Extensions.DependencyModel.RuntimeAssembly.#ctor(System.String,System.String)">
      <param name="assemblyName" />
      <param name="path" />
    </member>
    <member name="M:Microsoft.Extensions.DependencyModel.RuntimeAssembly.Create(System.String)">
      <param name="path" />
    </member>
    <member name="P:Microsoft.Extensions.DependencyModel.RuntimeAssembly.Name" />
    <member name="P:Microsoft.Extensions.DependencyModel.RuntimeAssembly.Path" />
    <member name="T:Microsoft.Extensions.DependencyModel.RuntimeAssetGroup" />
    <member name="M:Microsoft.Extensions.DependencyModel.RuntimeAssetGroup.#ctor(System.String,System.Collections.Generic.IEnumerable{Microsoft.Extensions.DependencyModel.RuntimeFile})">
      <param name="runtime" />
      <param name="runtimeFiles" />
    </member>
    <member name="M:Microsoft.Extensions.DependencyModel.RuntimeAssetGroup.#ctor(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <param name="runtime" />
      <param name="assetPaths" />
    </member>
    <member name="M:Microsoft.Extensions.DependencyModel.RuntimeAssetGroup.#ctor(System.String,System.String[])">
      <param name="runtime" />
      <param name="assetPaths" />
    </member>
    <member name="P:Microsoft.Extensions.DependencyModel.RuntimeAssetGroup.AssetPaths">
      <summary>Gets a list of asset paths provided in this runtime group.</summary>
      <returns>A read-only list of strings that represent the asset paths.</returns>
    </member>
    <member name="P:Microsoft.Extensions.DependencyModel.RuntimeAssetGroup.Runtime">
      <summary>The runtime ID associated with this group (may be empty if the group is runtime-agnostic).</summary>
      <returns>A string that represents the runtime ID.</returns>
    </member>
    <member name="P:Microsoft.Extensions.DependencyModel.RuntimeAssetGroup.RuntimeFiles">
      <summary>Gets a list of runtime files provided in this runtime group.</summary>
      <returns>A read-only list of runtime file instances.</returns>
    </member>
    <member name="T:Microsoft.Extensions.DependencyModel.RuntimeFallbacks" />
    <member name="M:Microsoft.Extensions.DependencyModel.RuntimeFallbacks.#ctor(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <param name="runtime" />
      <param name="fallbacks" />
    </member>
    <member name="M:Microsoft.Extensions.DependencyModel.RuntimeFallbacks.#ctor(System.String,System.String[])">
      <param name="runtime" />
      <param name="fallbacks" />
    </member>
    <member name="P:Microsoft.Extensions.DependencyModel.RuntimeFallbacks.Fallbacks" />
    <member name="P:Microsoft.Extensions.DependencyModel.RuntimeFallbacks.Runtime" />
    <member name="T:Microsoft.Extensions.DependencyModel.RuntimeFile" />
    <member name="M:Microsoft.Extensions.DependencyModel.RuntimeFile.#ctor(System.String,System.String,System.String)">
      <param name="path" />
      <param name="assemblyVersion" />
      <param name="fileVersion" />
    </member>
    <member name="P:Microsoft.Extensions.DependencyModel.RuntimeFile.AssemblyVersion" />
    <member name="P:Microsoft.Extensions.DependencyModel.RuntimeFile.FileVersion" />
    <member name="P:Microsoft.Extensions.DependencyModel.RuntimeFile.Path" />
    <member name="T:Microsoft.Extensions.DependencyModel.RuntimeLibrary" />
    <member name="M:Microsoft.Extensions.DependencyModel.RuntimeLibrary.#ctor(System.String,System.String,System.String,System.String,System.Collections.Generic.IReadOnlyList{Microsoft.Extensions.DependencyModel.RuntimeAssetGroup},System.Collections.Generic.IReadOnlyList{Microsoft.Extensions.DependencyModel.RuntimeAssetGroup},System.Collections.Generic.IEnumerable{Microsoft.Extensions.DependencyModel.ResourceAssembly},System.Collections.Generic.IEnumerable{Microsoft.Extensions.DependencyModel.Dependency},System.Boolean)">
      <param name="type" />
      <param name="name" />
      <param name="version" />
      <param name="hash" />
      <param name="runtimeAssemblyGroups" />
      <param name="nativeLibraryGroups" />
      <param name="resourceAssemblies" />
      <param name="dependencies" />
      <param name="serviceable" />
    </member>
    <member name="M:Microsoft.Extensions.DependencyModel.RuntimeLibrary.#ctor(System.String,System.String,System.String,System.String,System.Collections.Generic.IReadOnlyList{Microsoft.Extensions.DependencyModel.RuntimeAssetGroup},System.Collections.Generic.IReadOnlyList{Microsoft.Extensions.DependencyModel.RuntimeAssetGroup},System.Collections.Generic.IEnumerable{Microsoft.Extensions.DependencyModel.ResourceAssembly},System.Collections.Generic.IEnumerable{Microsoft.Extensions.DependencyModel.Dependency},System.Boolean,System.String,System.String)">
      <param name="type" />
      <param name="name" />
      <param name="version" />
      <param name="hash" />
      <param name="runtimeAssemblyGroups" />
      <param name="nativeLibraryGroups" />
      <param name="resourceAssemblies" />
      <param name="dependencies" />
      <param name="serviceable" />
      <param name="path" />
      <param name="hashPath" />
    </member>
    <member name="M:Microsoft.Extensions.DependencyModel.RuntimeLibrary.#ctor(System.String,System.String,System.String,System.String,System.Collections.Generic.IReadOnlyList{Microsoft.Extensions.DependencyModel.RuntimeAssetGroup},System.Collections.Generic.IReadOnlyList{Microsoft.Extensions.DependencyModel.RuntimeAssetGroup},System.Collections.Generic.IEnumerable{Microsoft.Extensions.DependencyModel.ResourceAssembly},System.Collections.Generic.IEnumerable{Microsoft.Extensions.DependencyModel.Dependency},System.Boolean,System.String,System.String,System.String)">
      <summary>Initializes a new <see cref="T:Microsoft.Extensions.DependencyModel.RuntimeLibrary" />.</summary>
      <param name="type">The library's type.</param>
      <param name="name">The library's name.</param>
      <param name="version">The library's version.</param>
      <param name="hash">The library package's hash.</param>
      <param name="runtimeAssemblyGroups">The library's runtime assemblies.</param>
      <param name="nativeLibraryGroups">The library's native libraries.</param>
      <param name="resourceAssemblies">The library's resource assemblies.</param>
      <param name="dependencies">The library's dependencies.</param>
      <param name="serviceable">Whether the library is serviceable.</param>
      <param name="path">The library package's path.</param>
      <param name="hashPath">The library package's hash path.</param>
      <param name="runtimeStoreManifestName">The library's runtime store manifest name.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" />, or <paramref name="name" />, or <paramref name="version" />, or <paramref name="runtimeAssemblyGroups" />, or <paramref name="nativeLibraryGroups" />, or <paramref name="resourceAssemblies" /> or <paramref name="dependencies" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:Microsoft.Extensions.DependencyModel.RuntimeLibrary.NativeLibraryGroups" />
    <member name="P:Microsoft.Extensions.DependencyModel.RuntimeLibrary.ResourceAssemblies" />
    <member name="P:Microsoft.Extensions.DependencyModel.RuntimeLibrary.RuntimeAssemblyGroups" />
    <member name="T:Microsoft.Extensions.DependencyModel.TargetInfo" />
    <member name="M:Microsoft.Extensions.DependencyModel.TargetInfo.#ctor(System.String,System.String,System.String,System.Boolean)">
      <param name="framework" />
      <param name="runtime" />
      <param name="runtimeSignature" />
      <param name="isPortable" />
    </member>
    <member name="P:Microsoft.Extensions.DependencyModel.TargetInfo.Framework" />
    <member name="P:Microsoft.Extensions.DependencyModel.TargetInfo.IsPortable" />
    <member name="P:Microsoft.Extensions.DependencyModel.TargetInfo.Runtime" />
    <member name="P:Microsoft.Extensions.DependencyModel.TargetInfo.RuntimeSignature" />
    <member name="T:System.Collections.Generic.CollectionExtensions">
      <summary>Provides extension methods for generic collections.</summary>
    </member>
    <member name="M:System.Collections.Generic.CollectionExtensions.GetDefaultAssets(System.Collections.Generic.IEnumerable{Microsoft.Extensions.DependencyModel.RuntimeAssetGroup})">
      <summary>Gets an enumeration of assets where each asset has an empty runtime.</summary>
      <param name="self">The source enumeration of <see cref="T:Microsoft.Extensions.DependencyModel.RuntimeAssetGroup" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="self" /> is <see langword="null" />.</exception>
      <returns>An enumeration of assets, each with an empty <see cref="P:Microsoft.Extensions.DependencyModel.RuntimeAssetGroup.Runtime" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.CollectionExtensions.GetDefaultGroup(System.Collections.Generic.IEnumerable{Microsoft.Extensions.DependencyModel.RuntimeAssetGroup})">
      <summary>Gets the first <see cref="T:Microsoft.Extensions.DependencyModel.RuntimeAssetGroup" /> with an empty <see cref="P:Microsoft.Extensions.DependencyModel.RuntimeAssetGroup.Runtime" />.</summary>
      <param name="self">The source enumeration of <see cref="T:Microsoft.Extensions.DependencyModel.RuntimeAssetGroup" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="self" /> is <see langword="null" />.</exception>
      <returns>The first <see cref="T:Microsoft.Extensions.DependencyModel.RuntimeAssetGroup" /> with an empty <see cref="P:Microsoft.Extensions.DependencyModel.RuntimeAssetGroup.Runtime" />, or <see langword="null" /> if none exists.</returns>
    </member>
    <member name="M:System.Collections.Generic.CollectionExtensions.GetDefaultRuntimeFileAssets(System.Collections.Generic.IEnumerable{Microsoft.Extensions.DependencyModel.RuntimeAssetGroup})">
      <summary>Gets an enumeration of <see cref="T:Microsoft.Extensions.DependencyModel.RuntimeFile" /> where each value has an empty runtime.</summary>
      <param name="self">The source enumeration of <see cref="T:Microsoft.Extensions.DependencyModel.RuntimeAssetGroup" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="self" /> is <see langword="null" />.</exception>
      <returns>An enumeration of <see cref="T:Microsoft.Extensions.DependencyModel.RuntimeFile" />, each with an empty <see cref="P:Microsoft.Extensions.DependencyModel.RuntimeAssetGroup.Runtime" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.CollectionExtensions.GetRuntimeAssets(System.Collections.Generic.IEnumerable{Microsoft.Extensions.DependencyModel.RuntimeAssetGroup},System.String)">
      <summary>Gets an enumeration of assets where each asset's value equals <paramref name="runtime" />.</summary>
      <param name="self">The source enumeration of <see cref="T:Microsoft.Extensions.DependencyModel.RuntimeAssetGroup" />.</param>
      <param name="runtime">The string to search for.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="self" /> is <see langword="null" />.</exception>
      <returns>An enumeration of assets where each asset's <see cref="P:Microsoft.Extensions.DependencyModel.RuntimeAssetGroup.Runtime" /> equals <paramref name="runtime" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.CollectionExtensions.GetRuntimeFileAssets(System.Collections.Generic.IEnumerable{Microsoft.Extensions.DependencyModel.RuntimeAssetGroup},System.String)">
      <summary>Gets an enumeration of <see cref="T:Microsoft.Extensions.DependencyModel.RuntimeFile" /> where each value's <see cref="P:Microsoft.Extensions.DependencyModel.RuntimeAssetGroup.Runtime" /> equals <paramref name="runtime" />.</summary>
      <param name="self">The source enumeration of <see cref="T:Microsoft.Extensions.DependencyModel.RuntimeAssetGroup" />.</param>
      <param name="runtime">The string to search for.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="runtime" /> is <see langword="null" /> or empty.</exception>
      <returns>An enumeration of <see cref="T:Microsoft.Extensions.DependencyModel.RuntimeFile" />, where each value's <see cref="P:Microsoft.Extensions.DependencyModel.RuntimeAssetGroup.Runtime" /> equals <paramref name="runtime" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.CollectionExtensions.GetRuntimeGroup(System.Collections.Generic.IEnumerable{Microsoft.Extensions.DependencyModel.RuntimeAssetGroup},System.String)">
      <summary>Gets the first <see cref="T:Microsoft.Extensions.DependencyModel.RuntimeAssetGroup" /> where <see cref="P:Microsoft.Extensions.DependencyModel.RuntimeAssetGroup.Runtime" /> matches <paramref name="runtime" />.</summary>
      <param name="self">The source enumeration of <see cref="T:Microsoft.Extensions.DependencyModel.RuntimeAssetGroup" />.</param>
      <param name="runtime">The string to search for.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="runtime" /> is <see langword="null" /> or empty.</exception>
      <returns>The first <see cref="T:Microsoft.Extensions.DependencyModel.RuntimeAssetGroup" /> where <see cref="P:Microsoft.Extensions.DependencyModel.RuntimeAssetGroup.Runtime" /> matches <paramref name="runtime" />, or <see langword="null" /> if none exists.</returns>
    </member>
  </members>
</doc>