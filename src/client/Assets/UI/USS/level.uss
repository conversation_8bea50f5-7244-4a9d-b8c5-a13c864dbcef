.timeleft {
    -unity-text-align: upper-center;
    font-size: 40px;
    -unity-font-style: bold;
    top: 5%;
}

.unity-list-view__item {
    background-color: rgba(0, 0, 0, 0);
    border-color: rgba(0, 0, 0, 0);
}

.unity-list-view__item:hover {
    background-color: rgba(0, 0, 0, 0);
    border-color: rgba(0, 0, 0, 0);
}

.unity-list-view__item:selected {
    background-color: rgba(0, 0, 0, 0);
    border-color: rgba(0, 0, 0, 0);
}

.unity-list-view__item:focus {
    background-color: rgba(0, 0, 0, 0);
    border-color: rgba(0, 0, 0, 0);
}

.unity-list-view__item:active {
    background-color: rgba(0, 0, 0, 0);
    border-color: rgba(0, 0, 0, 0);
}

.inventory-slot {
    width: 60px;
    height: 60px;
    margin: 2px;
    border-radius: 30px;
    background-color: rgba(255, 255, 255, 0.1);
    border-width: 2px;
    border-color: rgba(255, 255, 255, 0.3);
    align-items: center;
    justify-content: center;
    flex-direction: column;
    flex-grow: 1;
}

.inventory-slot-empty {
    opacity: 0.1;
    flex-grow: 1;
}

.inventory-slot-filled {
    opacity: 1;
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.6);
    flex-grow: 1;
}

.inventory-item {
    width: 60px;
    height: 60px;
    margin: 2px;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.inventory-item-icon {
    width: 50px;
    height: 50px;
    border-radius: 25px;
    background-color: rgba(255, 255, 255, 0.1);
    border-width: 2px;
    border-color: rgba(255, 255, 255, 0.3);
    align-items: center;
    justify-content: center;
}

.inventory-item-count {
    font-size: 12px;
    color: white;
    -unity-font-style: bold;
    margin-top: 2px;
    -unity-text-align: middle-center;
}

.hud-inventory-slot {
    flex-grow: 1;
}
