<engine:UXML xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:engine="UnityEngine.UIElements" xmlns:editor="UnityEditor.UIElements" noNamespaceSchemaLocation="../../../UIElementsSchema/UIElements.xsd" editor-extension-mode="False">
    <Style src="project://database/Assets/UI/USS/level.uss?fileID=7433441132597879392&amp;guid=74fb44a15ee7fed47b4ebf4ed525ef16&amp;type=3#level" />
    <Style src="project://database/Assets/UI/Level/Joystick/Joystick.uss?fileID=7433441132597879392&amp;guid=542ade93c93ca41f799c70ce7dabbe0c&amp;type=3#Joystick" />
    <engine:VisualElement name="Container" style="flex-grow: 1; justify-content: space-between;">
        <engine:VisualElement name="Controls" style="flex-grow: 1; height: auto; flex-direction: row; align-content: flex-start; justify-content: space-between; top: 0%; min-height: auto; max-height: none;">
            <engine:VisualElement name="joystick-root" class="joystick-root" style="min-width: 50%;">
                <engine:VisualElement name="joystick-bg" class="joystick-bg" style="align-items: center;">
                    <engine:VisualElement name="joystick-handle" class="joystick-handle" style="justify-content: space-around; align-items: center;" />
                </engine:VisualElement>
            </engine:VisualElement>
            <engine:VisualElement name="camera-rotator" enabled="true" style="flex-grow: 1; align-content: auto; align-self: auto; width: 50%; height: 100%; justify-content: center; position: relative; min-width: 50%;" />
        </engine:VisualElement>
    </engine:VisualElement>
</engine:UXML>
