<ui:UXML xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" noNamespaceSchemaLocation="../../../../UIElementsSchema/UIElements.xsd" editor-extension-mode="False">
    <ui:VisualElement name="InventorySlot" class="hud-inventory-slot hud-inventory-slot-empty" style="width: 60px; height: 60px; background-color: rgba(139, 69, 19, 0.8); border-radius: 8px; border-width: 2px; border-color: rgb(101, 67, 33); flex-direction: column; align-items: center; justify-content: space-between; padding: 4px; border-left-color: rgb(164, 164, 164); border-right-color: rgb(164, 164, 164); border-top-color: rgb(164, 164, 164); border-bottom-color: rgb(164, 164, 164);">
        <ui:VisualElement name="ItemContainer" class="hud-inventory-item" style="flex-grow: 1; width: 100%; align-items: center; justify-content: center;">
            <ui:VisualElement name="ItemImage" class="hud-inventory-item-image" style="width: 40px; height: 40px; flex-shrink: 0;" />
        </ui:VisualElement>
        <ui:Label name="ItemCount" class="hud-inventory-item-count" style="font-size: 14px; color: white; -unity-font-style: bold; -unity-text-align: middle-center; margin: 0; padding: 0; flex-shrink: 0; height: 16px;" />
    </ui:VisualElement>
</ui:UXML>
