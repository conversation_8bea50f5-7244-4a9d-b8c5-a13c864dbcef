<ui:UXML xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" noNamespaceSchemaLocation="../../../../UIElementsSchema/UIElements.xsd" editor-extension-mode="False">
    <ui:VisualElement name="InventorySlot" class="hud-inventory-slot hud-inventory-slot-empty" style="flex-grow: 1;">
        <ui:VisualElement name="ItemContainer" class="hud-inventory-item">
            <ui:VisualElement name="ItemIcon" class="hud-inventory-item-icon">
                <ui:VisualElement name="ItemImage" class="hud-inventory-item-image" />
            </ui:VisualElement>
            <ui:Label name="ItemCount" class="hud-inventory-item-count" />
        </ui:VisualElement>
    </ui:VisualElement>
</ui:UXML>
