<ui:UXML xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" noNamespaceSchemaLocation="../../../UIElementsSchema/UIElements.xsd" editor-extension-mode="False">
    <Style src="project://database/Assets/UI/USS/level.uss?fileID=7433441132597879392&amp;guid=74fb44a15ee7fed47b4ebf4ed525ef16&amp;type=3#level" />
    <ui:VisualElement name="Container" picking-mode="Ignore" style="flex-grow: 1; width: 100%; height: 100%; position: absolute; pointer-events: none;">
        <ui:VisualElement name="TimeContainer" style="position: absolute; top: 10%; left: 40%; width: 20%; align-items: center; justify-content: center; font-size: 58px;">
            <ui:Label text="03:00" name="TimeLeft" class="hud-time" />
        </ui:VisualElement>
        <ui:VisualElement name="InventoryContainer" class="hud-inventory-container" style="position: absolute; bottom: 5%; left: 30%; width: 40%; min-height: 10%; flex-direction: row; align-content: center;" />
    </ui:VisualElement>
</ui:UXML>
